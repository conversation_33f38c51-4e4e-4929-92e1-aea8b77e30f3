<script setup lang="ts">
import { Separator } from '@/components/ui/separator';
import { Skeleton } from '@/components/ui/skeleton';
</script>

<template>
  <div class="w-full rounded-md border bg-white p-3 shadow">
    <div class="flex items-center justify-between">
      <Skeleton class="h-6 w-24" />
      <Skeleton class="h-6 w-6 rounded-full" />
    </div>

    <Separator class="my-3" />

    <div class="grid grid-cols-2 gap-3">
      <div class="space-y-2" v-for="i in 4" :key="i">
        <Skeleton class="h-4 w-20" />
        <Skeleton class="h-10 w-full" />
      </div>

      <div class="col-span-2 grid grid-cols-2 gap-4">
        <Skeleton class="h-10 w-full" />
        <Skeleton class="h-10 w-full" />
      </div>
    </div>
  </div>
</template>
