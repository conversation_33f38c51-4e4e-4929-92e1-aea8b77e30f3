import { AttendanceType } from '@/enums/attendance';

export const getDayOfWeek = (dateString: string | number | Date) => {
  const date = new Date(dateString);
  const days = [
    'common.weekdays.sunday',
    'common.weekdays.monday',
    'common.weekdays.tuesday',
    'common.weekdays.wednesday',
    'common.weekdays.thursday',
    'common.weekdays.friday',
    'common.weekdays.saturday',
  ];

  return days[date.getDay()];
};

const typeClassMap: Record<AttendanceType, string> = {
  [AttendanceType.ON_TIME]: 'bg-emerald-100 text-emerald-800',
  [AttendanceType.LATE]: 'bg-amber-100 text-amber-800',
  [AttendanceType.EARLY]: 'bg-blue-100 text-blue-800',
  [AttendanceType.ABSENT]: 'bg-red-100 text-red-800',
};

const typeTextMap: Record<AttendanceType, string> = {
  [AttendanceType.ON_TIME]: 'attendance.type.on_time',
  [AttendanceType.LATE]: 'attendance.type.late',
  [AttendanceType.EARLY]: 'attendance.type.early',
  [AttendanceType.ABSENT]: 'attendance.type.absent',
};

export const getTypeClass = (status: AttendanceType): string => {
  return typeClassMap[status];
};

export const getTypeText = (status: AttendanceType): string => {
  return typeTextMap[status];
};
