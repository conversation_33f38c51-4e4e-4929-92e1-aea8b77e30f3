<script setup lang="ts">
import LeaveRequestsTab from '@/components/management/LeaveRequestsTab.vue';
import OvertimeRequestsTab from '@/components/management/OvertimeRequestsTab.vue';
import RejectModal from '@/components/management/RejectModal.vue';
import { useManagementRequests } from '@/composables/useManagementRequests';
import { Status } from '@/enums';
import { IonPage } from '@ionic/vue';
import { NTabPane, NTabs } from 'naive-ui';
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

// Use composables
const { rejectRequest } = useManagementRequests();

// Tab and modal states
const activeTab = ref<'leave' | 'overtime'>('leave');
const statusFilter = ref<Status | 'all'>(Status.PENDING);
const showRejectModal = ref(false);
const selectedRequestId = ref<number | null>(null);

const closeRejectModal = () => {
  showRejectModal.value = false;
  selectedRequestId.value = null;
};

const confirmReject = (reason: string) => {
  if (selectedRequestId.value) {
    rejectRequest(selectedRequestId.value, reason);
    closeRejectModal();
  }
};
</script>

<template>
  <ion-page>
    <div class="scroll-container my-3 flex h-full flex-col items-center gap-y-5">
      <n-tabs v-model:value="activeTab" type="line" size="large" justify-content="center" animated>

        <!-- New Management Leave Requests Tab -->
        <n-tab-pane name="leave" :tab="t('management.requests.tabs.leave')">
          <LeaveRequestsTab :status-filter="statusFilter" @update:status-filter="statusFilter = $event" />
        </n-tab-pane>

        <!-- Overtime Requests Tab -->
        <n-tab-pane name="overtime" :tab="t('management.requests.tabs.overtime')">
          <OvertimeRequestsTab :status-filter="statusFilter" @update:status-filter="statusFilter = $event" />
        </n-tab-pane>
      </n-tabs>
    </div>

    <!-- Rejection Modal (for old leave requests only) -->
    <RejectModal v-model:show="showRejectModal" @confirm="confirmReject" @cancel="closeRejectModal" />
  </ion-page>
</template>
