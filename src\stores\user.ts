import { computed, ref } from 'vue';

import { useAccount } from '@/composables/useAccount';
import { useProfile } from '@/composables/useProfile';
import { defineStore } from 'pinia';

export const useUserStore = defineStore('user', () => {
  const {
    isAuthenticated,
    error: authError,
    login,
    logout,
    forgotPassword,
  } = useAccount();

  const {
    user,
    isLoading: profileLoading,
    error: profileError,
    fullName,
    userInitials,
    getCurrentUser,
    modifiedLanguage,
  } = useProfile();

  const isLoading = computed(() => profileLoading.value);
  const error = computed(() => authError.value || profileError.value);
  const role = ref<string>('');
  const getRole = computed(() => role.value);

  const setRole = (newRole: string) => {
    role.value = newRole;
  };

  return {
    user,
    isAuthenticated,
    isLoading,
    error,
    fullName,
    userInitials,

    login,
    logout,
    forgotPassword,

    getCurrentUser,
    modifiedLanguage,

    getRole,
    setRole,
  };
});
