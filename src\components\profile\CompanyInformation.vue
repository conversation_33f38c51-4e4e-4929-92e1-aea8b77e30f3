<script setup lang="ts">
import {
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
} from '@/components/ui/drawer';
import { Separator } from '@/components/ui/separator';
import { useUserStore } from '@/stores/user';
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const userStore = useUserStore();

const detailsList = computed(() => [
  // { label: 'Company', content: userStore.user?.workplace_name || '-' },
  {
    label: t('profile.department'),
    content: userStore.user?.department_name || '-',
  },
  {
    label: t('profile.position'),
    content: userStore.user?.position_name || '-',
  },
  {
    label: t('profile.workplace'),
    content: userStore.user?.workplace_name || '-',
  },
  {
    label: t('profile.workplace_address'),
    content: userStore.user?.workplace_address || '-',
  },
]);
</script>

<template>
  <DrawerContent>
    <DrawerHeader>
      <DrawerTitle class="text-center">{{
        t('profile.company_information')
      }}</DrawerTitle>
      <DrawerDescription class="hidden" />
    </DrawerHeader>
    <Separator />
    <div class="flex w-full flex-col items-center justify-center gap-4 p-4">
      <div v-for="(item, index) in detailsList" :key="index" class="flex w-full items-center justify-between gap-x-2">
        <div class="text-sm text-gray-600">{{ item.label }}</div>
        <div class="font-medium text-gray-900">{{ item.content }}</div>
      </div>
    </div>
  </DrawerContent>
</template>
