import { createRouter, createWebHistory } from '@ionic/vue-router';
import type { NavigationGuardNext, RouteLocationNormalized } from 'vue-router';

import { authMiddleware } from '@/middleware/auth';
import { localeMiddleware } from '@/middleware/locale';
import { useHistoryStore } from '@/stores/history';
import { useLayoutStore } from '@/stores/title-layout';
import { nextTick } from 'vue';
import { createLocalizedRoutes } from './localized-routes';

const router = createRouter({
  history: createWebHistory(),
  routes: createLocalizedRoutes(),
});

// Add locale middleware before auth middleware
router.beforeEach(localeMiddleware);
router.beforeEach(authMiddleware);

router.beforeEach(
  (
    to: RouteLocationNormalized,
    _from: RouteLocationNormalized,
    next: NavigationGuardNext,
  ) => {
    const layout = useLayoutStore();
    layout.setPageTitle((to.meta.title as string) || 'sharp');
    next();
  },
);

router.afterEach((to: RouteLocationNormalized) => {
  nextTick(() => {
    if (to.meta.excludeFromHistory) return;

    const historyStore = useHistoryStore();
    historyStore.addPath(to.fullPath);
  });
});

export default router;
