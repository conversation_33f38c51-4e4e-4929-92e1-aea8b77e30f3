# Docker Configuration for Sharp Vue.js Application

This document provides comprehensive instructions for dockerizing and deploying the Sharp Vue.js application with PWA support.

## 📋 Table of Contents

- [Overview](#overview)
- [Prerequisites](#prerequisites)
- [Quick Start](#quick-start)
- [Configuration](#configuration)
- [Nginx Configuration](#nginx-configuration)
- [Scripts](#scripts)
- [Deployment](#deployment)
- [Troubleshooting](#troubleshooting)

## 🔍 Overview

The Docker setup includes:
- **Multi-stage Dockerfile** for optimized production builds
- **Nginx configuration** optimized for Vue.js SPA and PWA
- **Docker Compose** for easy container management
- **Build and deployment scripts** for automation
- **Health checks** and monitoring

## 📋 Prerequisites

- Docker Engine 20.10+
- Docker Compose 2.0+
- Node.js 22+ (for local development)
- Git

## 🚀 Quick Start

### 1. Build and Run with Docker Compose

```bash
# Build and start the application
docker-compose up --build -d

# Access the application
open http://localhost

# Check health
curl http://localhost/health

# View logs
docker-compose logs -f sharp-app

# Stop the application
docker-compose down
```

### 2. Build with Custom Scripts

```bash
# Make scripts executable
chmod +x docker-build.sh docker-deploy.sh

# Build the Docker image
./docker-build.sh

# Deploy the application
./docker-deploy.sh
```

## ⚙️ Configuration

### Environment Variables

Create a `.env` file for environment-specific configurations:

```env
# Application
NODE_ENV=production
VITE_APP_TITLE=Sharp
VITE_API_BASE_URL=https://api.example.com

# Docker
COMPOSE_PROJECT_NAME=sharp
NGINX_PORT=80
NGINX_SSL_PORT=443

# Timezone
TZ=Asia/Ho_Chi_Minh
```

### Docker Compose Override

Create `docker-compose.override.yml` for local development:

```yaml
version: '3.8'
services:
  sharp-app:
    ports:
      - "8080:80"
    environment:
      - NODE_ENV=development
```

## 🌐 Nginx Configuration

### Main Configuration (`nginx/nginx.conf`)

- **Performance optimizations**: Gzip compression, caching
- **Security headers**: XSS protection, content type options
- **Worker processes**: Auto-scaling based on CPU cores

### Server Configuration (`nginx/default.conf`)

- **SPA routing**: All routes serve `index.html`
- **PWA support**: Proper caching for service workers and manifest
- **Static asset optimization**: Long-term caching for assets
- **Health check endpoint**: `/health` for monitoring

### Customizing Nginx

To modify nginx configuration:

1. Edit files in the `nginx/` directory
2. Restart the container:
   ```bash
   docker-compose restart sharp-app
   ```

### Adding SSL/HTTPS

1. Place SSL certificates in `ssl/` directory
2. Update `nginx/default.conf` to include SSL configuration
3. Uncomment SSL volume mount in `docker-compose.yml`

## 🛠️ Scripts

### Build Script (`docker-build.sh`)

```bash
# Basic build
./docker-build.sh

# Custom image name and tag
./docker-build.sh -n myapp -t v1.0.0

# Build and push to registry
./docker-build.sh -p -r registry.example.com

# Pass build arguments
./docker-build.sh --build-arg NODE_ENV=production
```

### Deploy Script (`docker-deploy.sh`)

```bash
# Start services
./docker-deploy.sh up

# Stop services
./docker-deploy.sh down

# View logs
./docker-deploy.sh logs

# Restart services
./docker-deploy.sh restart

# Check status
./docker-deploy.sh status
```

## 🚀 Deployment

### Production Deployment

1. **Build the image:**
   ```bash
   ./docker-build.sh -n sharp-vue-app -t production
   ```

2. **Deploy with compose:**
   ```bash
   docker-compose -f docker-compose.yml up -d
   ```

3. **Verify deployment:**
   ```bash
   curl http://localhost/health
   ```

### CI/CD Pipeline

Example GitHub Actions workflow:

```yaml
name: Deploy
on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Build and Deploy
        run: |
          ./docker-build.sh -t ${{ github.sha }}
          ./docker-deploy.sh build
```

## 🔧 Troubleshooting

### Common Issues

1. **Port already in use:**
   ```bash
   # Check what's using port 80
   sudo lsof -i :80
   
   # Use different port
   docker-compose up -d --scale sharp-app=0
   docker-compose up -d -p 8080:80
   ```

2. **Build failures:**
   ```bash
   # Clean Docker cache
   docker system prune -a
   
   # Rebuild without cache
   docker-compose build --no-cache
   ```

3. **Nginx configuration errors:**
   ```bash
   # Test nginx config
   docker-compose exec sharp-app nginx -t
   
   # View nginx logs
   docker-compose logs sharp-app
   ```

### Health Checks

Monitor application health:

```bash
# Check health endpoint
curl http://localhost/health

# View container health
docker-compose ps

# Check logs
docker-compose logs -f sharp-app
```

### Performance Monitoring

```bash
# Container stats
docker stats

# Nginx access logs
docker-compose exec sharp-app tail -f /var/log/nginx/access.log

# Error logs
docker-compose exec sharp-app tail -f /var/log/nginx/error.log
```

## 📝 Notes

- The application is optimized for production with gzip compression and caching
- PWA features are fully supported with proper service worker caching
- All routes are handled by the SPA router (Vue Router)
- Static assets are cached for 1 year for optimal performance
- Health checks ensure container reliability

For more information, refer to the main [README.md](./README.md) file.
