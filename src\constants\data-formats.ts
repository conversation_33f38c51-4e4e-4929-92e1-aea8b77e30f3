export const DATE_FORMATS = {
  // Display formats (for showing to users)
  DISPLAY_DATE_DMY: 'dd-MM-yyyy',
  DISPLAY_DATE_MDY: 'dd/MM/yyyy',
  DISPLAY_DATETIME: 'dd/MM/yyyy HH:mm',
  DISPLAY_DATETIME_SEC: 'dd/MM/yyyy HH:mm:ss',
  DISPLAY_TIME: 'HH:mm',
  DISPLAY_TIME_SEC: 'HH:mm:ss',
  DISPLAY_TIME_12H: 'hh:mm a',

  // Value formats (for internal value storage)
  VALUE_DATE: 'yyyy-MM-dd',
  VALUE_DATETIME: 'yyyy-MM-dd HH:mm:ss',
  VALUE_TIME: 'HH:mm:ss',

  // API formats
  API_DATE: 'yyyy-MM-dd',
  API_DATETIME: 'yyyy-MM-dd HH:mm:ss',
  API_TIME: 'HH:mm:ss',
} as const;

export const DATE_PLACEHOLDERS = {
  DATE: 'dd/MM/yyyy',
  DATE_DMY: 'dd-MM-yyyy',
  DATETIME: 'dd/MM/yyyy HH:mm',
  TIME: 'HH:mm',
  TIME_SEC: 'HH:mm:ss',
  TIME_12H: 'hh:mm AM/PM',
} as const;
