<script setup lang="ts">
import LeaveSkeleton from '@/components/leaves/LeaveSkeleton.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useDateFormats } from '@/composables/useDateFormats';
import { useLeaveForm } from '@/composables/useLeaveForm';
import { FormMode } from '@/enums';
import { LeaveOfType } from '@/enums/leave';
import { isEmployee } from '@/helpers/staff-helper';
import { cn } from '@/lib/utils';
import { IonPage } from '@ionic/vue';
import {
  NCheckbox,
  NDatePicker,
  NForm,
  NFormItem,
  NInput,
  NSelect,
} from 'naive-ui';
import { computed, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const {
  form,
  formRef,
  hasEmployees,
  rules,
  relTypeOptions,
  leaveTypeOptions,
  staffOptions,
  staffManagerOptions,
  attendanceStatusOptions,
  remainDays,
  totalDaysOff,
  isSubmitDisabled,
  isLoading,
  RelTypeEnum,
  handleSubmit,
  user,
} = useLeaveForm(FormMode.CREATE);

const { dateFormats } = useDateFormats();

// Session options for individual date selection
const dateSessionOptions = [
  { label: t('leaves.new_request.session_1'), value: 'session_1' },
  { label: t('leaves.new_request.session_2'), value: 'session_2' },
];

// Session selection for start and end dates
const startDateSession = ref('session_1');
const endDateSession = ref('session_1');

const toVietnamDate = (date: number | Date): Date => {
  const d = new Date(date);
  // Convert to Vietnam timezone (UTC+7)
  const vietnamTime = new Date(d.getTime() + (7 * 60 * 60 * 1000));
  return new Date(vietnamTime.getFullYear(), vietnamTime.getMonth(), vietnamTime.getDate());
};

// Function to calculate leave days based on actual work schedule
const calculateLeaveDaysBasedOnSchedule = async (startDate: Date, endDate: Date): Promise<number> => {
  try {
    // Format dates for API call
    const formatDate = (date: Date): string => {
      return date.toISOString().split('T')[0];
    };

    // Call API to get detailed attendance/schedule data
    const filters = {
      date_from: formatDate(startDate),
      date_to: formatDate(endDate),
      staff_id: form.value.employee_ids?.[0] || user?.value?.id
    };

    // Import the API function
    const { getDetailsAttandanceLogs } = await import('@/services/attendance.service');
    const response = await getDetailsAttandanceLogs(filters);

    if (!response.data) {
      // Fallback to simple calculation if API fails
      return calculateLeaveDaysSimple(startDate, endDate);
    }

    let totalDays = 0;
    const attendanceDays = response.data.data || [];

    // Process each day in the range
    const currentDate = new Date(startDate);
    while (currentDate <= endDate) {
      const dateString = formatDate(currentDate);
      const dayData = attendanceDays.find(day => day.date === dateString);

      if (dayData && dayData.shifts && dayData.shifts.assigned_shifts.length > 0) {
        // Employee has work schedule for this day
        const shifts = dayData.shifts.assigned_shifts;
        const isSameDay = startDate.getTime() === endDate.getTime();

        if (isSameDay) {
          // Same day - calculate based on sessions
          if (startDateSession.value === 'session_1' && endDateSession.value === 'session_2') {
            // Full day
            totalDays += 1;
          } else {
            // Half day
            totalDays += 0.5;
          }
        } else if (currentDate.getTime() === startDate.getTime()) {
          // First day
          if (startDateSession.value === 'session_1') {
            // Check if there are both morning and afternoon shifts
            const hasFullDayShift = shifts.some(shift => shift.total_hours >= 8);
            totalDays += hasFullDayShift ? 1 : 0.5;
          } else {
            // Starting from afternoon session
            totalDays += 0.5;
          }
        } else if (currentDate.getTime() === endDate.getTime()) {
          // Last day
          if (endDateSession.value === 'session_2') {
            // Check if there are both morning and afternoon shifts
            const hasFullDayShift = shifts.some(shift => shift.total_hours >= 8);
            totalDays += hasFullDayShift ? 1 : 0.5;
          } else {
            // Ending at morning session
            totalDays += 0.5;
          }
        } else {
          // Middle days - count based on actual work schedule
          const hasFullDayShift = shifts.some(shift => shift.total_hours >= 8);
          totalDays += hasFullDayShift ? 1 : 0.5;
        }
      }

      currentDate.setDate(currentDate.getDate() + 1);
    }

    return totalDays;
  } catch (error) {
    console.error('Error calculating leave days based on schedule:', error);
    // Fallback to simple calculation
    return calculateLeaveDaysSimple(startDate, endDate);
  }
};
// Fallback function for simple calculation (without weekend exclusion as requested)
const calculateLeaveDaysSimple = (startDate: Date, endDate: Date): number => {
  let totalDays = 0;
  const currentDate = new Date(startDate);
  const isSameDay = startDate.getTime() === endDate.getTime();

  while (currentDate <= endDate) {
    if (isSameDay) {
      if (startDateSession.value === 'session_1' && endDateSession.value === 'session_2') {
        totalDays += 1;
      } else if (startDateSession.value === 'session_1' && endDateSession.value === 'session_1') {
        totalDays += 0.5;
      } else if (startDateSession.value === 'session_2' && endDateSession.value === 'session_2') {
        totalDays += 0.5;
      }
      break;
    } else if (currentDate.getTime() === startDate.getTime()) {
      // First day
      if (startDateSession.value === 'session_1') {
        totalDays += 1;
      } else {
        totalDays += 0.5;
      }
    } else if (currentDate.getTime() === endDate.getTime()) {
      // Last day
      if (endDateSession.value === 'session_2') {
        totalDays += 1;
      } else {
        totalDays += 0.5;
      }
    } else {
      totalDays += 1;
    }
    currentDate.setDate(currentDate.getDate() + 1);
  }

  return totalDays;
};

const calculateLeaveDays = async (): Promise<number> => {
  if (!form.value.start_time || !form.value.end_time || !isLeaveType.value) {
    return 0;
  }

  const startDate = toVietnamDate(form.value.start_time);
  const endDate = toVietnamDate(form.value.end_time);

  if (startDate > endDate) {
    return 0;
  }

  return await calculateLeaveDaysBasedOnSchedule(startDate, endDate);
};

const calculatedLeaveDays = ref(0);

// Function to update calculated leave days
const updateCalculatedLeaveDays = async () => {
  const days = await calculateLeaveDays();
  calculatedLeaveDays.value = days;
};

// const shouldShowPartialDayWarning = computed(() => {
//   if (!isLeaveType.value || calculatedLeaveDays.value <= 1) {
//     return false;
//   }

//   if (startDateSession.value === 'session_1' && endDateSession.value === 'session_1') {
//     return false;
//   }

//   return calculatedLeaveDays.value % 1 !== 0;
// });

const isSameDateSelected = computed(() => {
  if (!form.value.start_time || !form.value.end_time) {
    return false;
  }

  const startDate = toVietnamDate(form.value.start_time);
  const endDate = toVietnamDate(form.value.end_time);

  return startDate.getTime() === endDate.getTime();
});

// Computed properties for conditional rendering
const isLeaveType = computed(() => form.value.rel_type === RelTypeEnum.LEAVE);
const isNonEmployee = computed(() => !isEmployee(user?.value?.role.name || ''));
const shouldShowAttendanceStatus = computed(() => {
  const isEarlyOrLate =
    form.value.rel_type === RelTypeEnum.EARLY ||
    form.value.rel_type === RelTypeEnum.LATE;
  const isUnpaidLeave = form.value.type_of_leave === LeaveOfType.UNPAID_LEAVE;
  return isNonEmployee.value && (isEarlyOrLate || isUnpaidLeave);
});


watch(startDateSession, (newSession) => {
  if (isSameDateSelected.value) {
    endDateSession.value = newSession;
  }
});

watch(
  [() => form.value.start_time, () => form.value.end_time, startDateSession, endDateSession],
  async () => {
    if (isLeaveType.value && form.value.start_time && form.value.end_time) {
      await updateCalculatedLeaveDays();
      if (calculatedLeaveDays.value > 0) {
        form.value.number_of_leaving_day = calculatedLeaveDays.value;
      }
    }
  },
  { immediate: true }
);
</script>

<template>
  <ion-page>
    <div :class="cn(
      'scroll-container mb-7 flex h-full flex-col items-center gap-y-7 p-4',
      isLoading ? 'justify-center' : 'justify-start',
    )
      ">
      <div v-if="isLoading" class="w-full space-y-6 animate-pulse">
        <LeaveSkeleton />
      </div>
      <n-form v-else ref="formRef" label-placement="top" :model="form" :rules="rules" class="w-full">
        <n-form-item :label="t('leaves.new_request.subject')" path="subject" required>
          <n-input v-model:value="form.subject" :placeholder="t('leaves.new_request.subject_placeholder')" />
        </n-form-item>

        <n-checkbox v-if="isNonEmployee" v-model:checked="hasEmployees" size="small"
          :class="cn('w-full', hasEmployees ? 'mb-2' : 'mb-4')">
          {{ t('leaves.new_request.register_for_employee') }}
        </n-checkbox>

        <n-form-item v-if="isNonEmployee && hasEmployees" :label="t('leaves.new_request.staff')" path="employee_ids">
          <n-select filterable multiple v-model:value="form.employee_ids" :options="staffOptions"
            :placeholder="t('leaves.new_request.staff_placeholder')" />
        </n-form-item>

        <div class="grid grid-cols-2 items-center gap-2">
          <n-form-item :label="t('leaves.new_request.rel_type')" path="rel_type">
            <n-select v-model:value="form.rel_type" :options="relTypeOptions"
              :placeholder="t('leaves.new_request.rel_type_placeholder')" />
          </n-form-item>

          <n-form-item v-if="isLeaveType" :label="t('leaves.new_request.type_of_leave')" path="type_of_leave">
            <n-select v-model:value="form.type_of_leave" :options="leaveTypeOptions"
              :placeholder="t('leaves.new_request.type_of_leave_placeholder')" />
          </n-form-item>
        </div>

        <div v-if="isLeaveType" class="mb-4 space-y-1 font-semibold">
          <p>
            {{ t('leaves.new_request.total_days_off') }}: {{ totalDaysOff }}
          </p>
          <p>{{ t('leaves.new_request.remaining_days') }}: {{ remainDays }}</p>
        </div>
        <!-- Start Date Selection -->
        <div class="grid grid-cols-2 items-center gap-2">
          <n-form-item :label="t('leaves.new_request.start_date')" path="start_time" required>
            <n-date-picker v-model:value="form.start_time" :format="dateFormats.date.display"
              :placeholder="dateFormats.date.placeholder" clearable class="w-full" />
          </n-form-item>
          <n-form-item v-if="isLeaveType" :label="t('leaves.new_request.start_date_session')" path="start_date_session">
            <n-select v-model:value="startDateSession" :options="dateSessionOptions"
              :placeholder="t('leaves.new_request.session_1')" />
          </n-form-item>
        </div>

        <!-- End Date Selection -->
        <div class="grid grid-cols-2 items-center gap-2">
          <n-form-item v-if="isLeaveType" :label="t('leaves.new_request.end_date')" path="end_time" required>
            <n-date-picker v-model:value="form.end_time" :format="dateFormats.date.display"
              :placeholder="dateFormats.date.placeholder" clearable class="w-full" />
          </n-form-item>
          <n-form-item v-if="isLeaveType" :label="t('leaves.new_request.end_date_session')" path="end_date_session">
            <n-select v-model:value="endDateSession" :options="dateSessionOptions"
              :placeholder="t('leaves.new_request.session_placeholder')" />
          </n-form-item>
        </div>

        <!-- Display calculated leave days -->
        <div v-if="isLeaveType && form.start_time && form.end_time" class="mb-4 rounded bg-green-50 p-3">
          <p class="text-sm text-green-700">
            <span class="font-medium">{{ t('leaves.new_request.calculated_days') }}:</span>
            {{ calculatedLeaveDays }} {{ t('leaves.new_request.working_days') }}

          </p>
        </div>

        <!-- Warning for partial days when more than 1 day -->
        <!-- <div v-if="shouldShowPartialDayWarning" class="mb-4 rounded bg-amber-500 border border-amber-100 p-3">
          <p class="text-sm text-amber-800">
            <span class="font-medium">{{ t('leaves.new_request.partial_day_warning') }}:</span>
            {{ t('leaves.new_request.partial_day_description') }}
          </p>
        </div> -->

        <n-form-item :label="t('leaves.new_request.follower')" path="follower_id">
          <n-select filterable v-model:value="form.follower_id" :options="staffManagerOptions"
            :placeholder="t('leaves.new_request.follower_placeholder')" />
        </n-form-item>

        <!-- approver level 1 -->
        <n-form-item :label="t('leaves.new_request.approver')" path="approver_id">
          <n-select filterable v-model:value="form.approver_id" :options="staffManagerOptions"
            :placeholder="t('leaves.new_request.approver_placeholder')" />
        </n-form-item>

        <!-- approver level 2 -->
        <!-- <n-form-item :label="t('leaves.new_request.approver_2')" path="approver_id">
          <n-select filterable v-model:value="form.approver_id" :options="staffManagerOptions"
            :placeholder="t('leaves.new_request.approver_placeholder')" />
        </n-form-item> -->

        <!-- approver level 3 -->
        <!-- <n-form-item :label="t('leaves.new_request.approver_3')" path="approver_id">
          <n-select filterable v-model:value="form.approver_id" :options="staffManagerOptions"
            :placeholder="t('leaves.new_request.approver_placeholder')" />
        </n-form-item> -->

        <!-- approver level 4 -->
        <!-- <n-form-item :label="t('leaves.new_request.approver_4')" path="approver_id">
          <n-select filterable v-model:value="form.approver_id" :options="staffManagerOptions"
            :placeholder="t('leaves.new_request.approver_placeholder')" />
        </n-form-item> -->

        <n-form-item v-if="shouldShowAttendanceStatus" :label="t('leaves.new_request.attendance_status')"
          path="is_deducted_attendance">
          <n-select v-model:value="form.is_deducted_attendance" :options="attendanceStatusOptions"
            :placeholder="t('leaves.new_request.attendance_status_placeholder')" />
        </n-form-item>

        <n-form-item :label="t('leaves.new_request.reason')" path="reason">
          <n-input v-model:value="form.reason" type="textarea"
            :placeholder="t('leaves.new_request.reason_placeholder')" />
        </n-form-item>

        <n-form-item v-if="form.type_of_leave == LeaveOfType.SICK_LEAVE" :label="t('common.choose_file')">
          <Input type="file" />
        </n-form-item>

        <Button type="submit" size="lg" class="w-full" @click="handleSubmit" :disabled="isSubmitDisabled">
          {{ t('leaves.new_request.submit') }}
        </Button>
      </n-form>
    </div>
  </ion-page>
</template>

<style scoped>
.n-form-item-label::after {
  content: '*';
  color: red;
  margin-left: 4px;
}

.n-date-picker {
  width: 100%;
}

.n-checkbox {
  --n-label-font-weight: 500 !important;
}
</style>
