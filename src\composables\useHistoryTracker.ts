import { useHistoryStore } from '@/stores/history';
import { useRoute } from 'vue-router';
import { watch } from 'vue';

export function useHistoryTracker() {
  const route = useRoute();
  const historyStore = useHistoryStore();

  historyStore.loadFromStorage();

  watch(
    () => route.fullPath,
    (newPath) => {
      historyStore.addPath(newPath);
    },
    { immediate: true },
  );

  return {
    rawHistory: historyStore.rawHistory,
    cleanedHistory: historyStore.cleanedHistory,
    clearHistory: historyStore.clearHistory,
    getPreviousUrl: historyStore.getPreviousUrl,
  };
}
