<script setup lang="ts">
import { NTag } from 'naive-ui';
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

// Fake data for employee advances
const employeeAdvances = ref([
  {
    id: 1,
    purpose: 'Business Trip to Ho Chi Minh City',
    date: '2023-10-15',
    amount: 3000000,
    remainingAmount: 1200000,
    status: t('expense.active'),
  },
  {
    id: 2,
    purpose: 'Office Equipment Purchase',
    date: '2023-11-05',
    amount: 5000000,
    remainingAmount: 2500000,
    status: t('expense.active'),
  },
]);

// Check if there are advances
const hasAdvances = ref(employeeAdvances.value.length > 0);

// Format currency
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND',
  }).format(amount);
};
</script>

<template>
  <div class="w-full">
    <div class="flex items-center justify-between">
      <h4 class="font-bold text-gray-800">
        {{ t('expense.advance_balance') }}
      </h4>
      <router-link
        to="#"
        class="text-sm font-bold text-gray-800 underline hover:!text-gray-800"
      >
        {{ t('expense.view_list') }}
      </router-link>
    </div>
    <div
      v-if="!hasAdvances"
      class="flex flex-col items-center rounded p-5 text-sm text-gray-600"
    >
      {{ t('expense.no_expenses') }}
    </div>
    <div v-else class="mt-3 flex flex-col gap-3">
      <div
        v-for="advance in employeeAdvances"
        :key="advance.id"
        class="flex flex-col gap-1 rounded-md border border-gray-200 p-3"
      >
        <div class="flex items-center justify-between">
          <span class="font-medium">{{ advance.purpose }}</span>
          <n-tag type="info" size="small" round>
            {{ advance.status }}
          </n-tag>
        </div>
        <div class="text-xs text-gray-500">{{ advance.date }}</div>
        <div class="mt-2 flex items-center justify-between">
          <div class="flex flex-col">
            <span class="text-xs text-gray-500">{{
              t('expense.total_amount')
            }}</span>
            <span class="text-sm font-medium">{{
              formatCurrency(advance.amount)
            }}</span>
          </div>
          <div class="flex flex-col items-end">
            <span class="text-xs text-gray-500">{{
              t('expense.remaining')
            }}</span>
            <span class="text-sm font-medium text-green-600">
              {{ formatCurrency(advance.remainingAmount) }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
