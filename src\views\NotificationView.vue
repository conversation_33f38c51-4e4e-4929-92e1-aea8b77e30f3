<script setup lang="ts">
import NotificationList from '@/components/notifications/NotificationList.vue';
import { useNotifications } from '@/composables/useNotifications';
import { IonPage } from '@ionic/vue';
import { ref, watch } from 'vue';
import { toast } from 'vue-sonner';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const { data: notifications, isLoading: loading, refetch, handleRemoveNotification } = useNotifications();
const localNotifications = ref(notifications.value || []);
const pullRefresh = ref({ active: false, distance: 0 });
const startY = ref(0);

watch(notifications, (newNotifications) => {
  if (newNotifications) {
    localNotifications.value = [...newNotifications];
  }
}, { immediate: true });

const markAsRead = (id: string) => {
  const index = localNotifications.value.findIndex((n) => String(n.id) === id);
  if (index !== -1) {
    localNotifications.value[index] = {
      ...localNotifications.value[index],
      is_read: 1,
    };
  }
};

const removeNotification = async (id: string) => {
  try {
    await handleRemoveNotification(Number(id));
    localNotifications.value = localNotifications.value.filter((n) => n.id !== Number(id));
    toast.success(t('notification.remove_success'));
  } catch (error) {
    console.error('Failed to remove notification:', error);
    toast.error(t('notification.remove_error'));
  }
};

const markAllAsRead = () => {
  localNotifications.value = localNotifications.value.map((n) => ({
    ...n,
    is_read: 1,
  }));
};

const clearAll = () => {
  localNotifications.value = [];
};

const onTouchStart = (e: TouchEvent) => {
  if (window.scrollY === 0) {
    startY.value = e.touches[0].clientY;
  }
};

const onTouchMove = (e: TouchEvent) => {
  if (startY.value === 0 || window.scrollY > 0) return;

  const currentY = e.touches[0].clientY;
  const distance = currentY - startY.value;

  if (distance > 0) {
    pullRefresh.value.active = true;
    pullRefresh.value.distance = Math.min(distance * 0.5, 80);
    e.preventDefault();
  }
};

const onTouchEnd = () => {
  if (pullRefresh.value.active && pullRefresh.value.distance > 50) {
    refreshNotifications();
  }

  pullRefresh.value.active = false;
  pullRefresh.value.distance = 0;
  startY.value = 0;
};

const refreshNotifications = () => {
  refetch();
};
</script>

<template>
  <ion-page class="mt-5 !justify-start gap-y-7">
    <div class="scroll-container mb-7 px-4 py-2" @touchstart="onTouchStart" @touchmove="onTouchMove"
      @touchend="onTouchEnd">
      <div v-if="pullRefresh.active" class="absolute top-0 right-0 left-0 z-10 flex flex-col items-center justify-end"
        :style="{ height: `${pullRefresh.distance}px` }">
        <div class="mb-2" :class="{ 'animate-spin': pullRefresh.distance > 50 }">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-600" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2">
            <path d="M21.5 2v6h-6M2.5 22v-6h6M2 11.5a10 10 0 0 1 18.8-4.3M22 12.5a10 10 0 0 1-18.8 4.2" />
          </svg>
        </div>
        <span class="text-sm text-gray-600">
          {{
            pullRefresh.distance > 50 ? 'Release to refresh' : 'Pull to refresh'
          }}
        </span>
      </div>

      <div v-if="loading" class="fixed inset-0 z-50 flex items-center justify-center bg-white/70 backdrop-blur-sm">
        <div class="border-primary h-10 w-10 animate-spin rounded-full border-4 border-t-transparent"></div>
      </div>

      <NotificationList :notifications="localNotifications" @mark-as-read="markAsRead" @remove="removeNotification"
        @mark-all-as-read="markAllAsRead" @clear-all="clearAll" />
    </div>
  </ion-page>
</template>
