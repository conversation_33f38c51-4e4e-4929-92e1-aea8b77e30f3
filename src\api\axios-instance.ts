import VueCookies from 'vue-cookies';
import axios from 'axios';

const baseURL = import.meta.env.VITE_API_BASE_URL;

const cookies = VueCookies;

const apiClient = axios.create({
  baseURL,
  headers: {
    'Content-Type': 'application/json',
  },
});

apiClient.interceptors.request.use(
  (config) => {
    const token = cookies.get('auth-token');
    if (token) {
      config.headers.Authorization = token;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  },
);

apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response && error.response.status === 401) {
      cookies.remove('auth-token');
    }
    return Promise.reject(error);
  },
);

export default apiClient;
