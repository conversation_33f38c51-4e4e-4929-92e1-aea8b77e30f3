<script setup lang="ts">
import { getVisiblePages } from '@/utils/pagination';
import { ChevronLeft, ChevronRight } from 'lucide-vue-next';
import { defineProps } from 'vue';
import { useI18n } from 'vue-i18n';

// Props
defineProps({
  totalPages: {
    type: Number,
    required: true,
  },
  currentPage: {
    type: Number,
    required: true,
  },
  totalRecords: {
    type: Number,
    required: true,
  },
  loading: {
    type: Boolean,
    default: false,
  },
});

// Emits
const emit = defineEmits(['prevPage', 'nextPage', 'goToPage']);

// Methods
const handlePrevPage = () => emit('prevPage');
const handleNextPage = () => emit('nextPage');
const handleGoToPage = (page: number) => emit('goToPage', page);

const { t } = useI18n();
</script>

<template>
  <div v-if="totalPages > 1" class="mb-7 w-full">
    <div class="rounded-xl border border-gray-200 bg-white p-4 shadow-sm">
      <!-- Pagination Info -->
      <div class="mb-4 flex items-center justify-between">
        <div class="text-sm text-gray-600">
          {{ t('common.pagination.total', { total: totalRecords }) }}
        </div>
        <div class="text-sm text-gray-600">
          {{
            t('common.pagination.page', {
              page: currentPage,
              totalPages: totalPages,
            })
          }}
        </div>
      </div>

      <!-- Pagination Buttons -->
      <div class="flex items-center justify-center gap-x-3">
        <button
          @click="handlePrevPage"
          :disabled="currentPage === 1 || loading"
          class="cursor-pointer rounded-lg border border-gray-300 px-3 py-2 text-gray-700 transition-colors hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50"
        >
          <ChevronLeft class="h-4 w-4" />
        </button>

        <!-- Page Numbers -->
        <div class="flex items-center gap-x-2">
          <template
            v-for="page in getVisiblePages(currentPage, totalPages)"
            :key="page"
          >
            <button
              v-if="page !== '...'"
              @click="handleGoToPage(Number(page))"
              :disabled="loading"
              :class="[
                'size-9 cursor-pointer rounded-lg text-sm font-medium transition-colors',
                page === currentPage
                  ? 'bg-c-secondary text-white'
                  : 'text-gray-700 hover:bg-gray-500/10 disabled:opacity-50',
              ]"
            >
              {{ page }}
            </button>
            <span v-else class="px-2 text-gray-400">...</span>
          </template>
        </div>

        <button
          @click="handleNextPage"
          :disabled="currentPage === totalPages || loading"
          class="cursor-pointer rounded-lg border border-gray-300 px-3 py-2 text-gray-700 transition-colors hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50"
        >
          <ChevronRight class="h-4 w-4" />
        </button>
      </div>
    </div>
  </div>
</template>
