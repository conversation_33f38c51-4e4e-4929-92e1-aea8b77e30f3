<script setup lang="ts">
import Skeleton from '../ui/skeleton/Skeleton.vue';

</script>

<template>

  <div>
    <Skeleton class="h-4 w-32 mb-2" />
    <Skeleton class="h-10 w-full" />
  </div>

  <div>
    <Skeleton class="h-4 w-48 mb-3" />
    <Skeleton class="h-20 w-full rounded-lg" />
  </div>

  <div>
    <Skeleton class="h-4 w-28 mb-2" />
    <Skeleton class="h-10 w-full" />
  </div>

  <div>
    <Skeleton class="h-4 w-40 mb-2" />
    <Skeleton class="h-10 w-full" />
  </div>

  <div>
    <Skeleton class="h-4 w-20 mb-2" />
    <Skeleton class="h-10 w-full" />
  </div>

  <div>
    <Skeleton class="h-4 w-28 mb-2" />
    <Skeleton class="h-10 w-full" />
  </div>
  <div>
    <Skeleton class="h-4 w-28 mb-2" />
    <Skeleton class="h-10 w-full" />
  </div>

  <div>
    <Skeleton class="h-4 w-32 mb-2" />
    <Skeleton class="h-10 w-full" />
  </div>
  <div>
    <Skeleton class="h-4 w-32 mb-2" />
    <Skeleton class="h-10 w-full" />
  </div>

  <div>
    <Skeleton class="h-4 w-32 mb-2" />
    <Skeleton class="h-20 w-full" />
  </div>

  <div class="flex gap-x-4">
    <Skeleton class="h-12 w-full" />
    <Skeleton class="h-12 w-full" />
  </div>
</template>
