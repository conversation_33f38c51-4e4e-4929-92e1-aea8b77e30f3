<script setup lang="ts">
import { useHoliday } from '@/composables/useHoliday';
import { useLocale } from '@/composables/useLocale';
import { formatDate } from '@/utils/format';
import {
  NBadge,
  NCard,
  NEmpty,
  NSpace,
  NText,
  NTimeline,
  NTimelineItem,
} from 'naive-ui';
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const { holidaysQuery } = useHoliday();
const { locale } = useLocale();
const holidays = computed(() => holidaysQuery.data.value || []);
</script>

<template>
  <n-card class="mb-7">
    <n-space vertical>
      <n-text strong class="text-lg !font-semibold">
        {{ t('leaves.upcoming_holiday.title') }}
      </n-text>

      <div v-if="holidays.length > 0">
        <n-timeline>
          <n-timeline-item
            v-for="holiday in holidays"
            :key="holiday.id"
            :title="holiday.holiday_name"
            :content="
              formatDate(holiday.holiday_date, locale) +
              ' (' +
              t(`common.weekdays.${holiday.day_of_week_label}`) +
              ')'
            "
            :time="
              t('leaves.holiday.days_remaining', {
                days: holiday.days_remaining,
              })
            "
          >
            <template #icon>
              <n-badge :value="holiday.days_remaining" :max="99" type="info" />
            </template>
          </n-timeline-item>
        </n-timeline>
      </div>

      <n-empty v-else :description="t('leaves.holiday.no_upcoming_holidays')" />
    </n-space>
  </n-card>
</template>
