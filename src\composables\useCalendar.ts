import { ref, computed, type Ref, type ComputedRef } from "vue"

interface UseCalendarReturn {
  currentDate: Ref<Date>
  year: ComputedRef<number>
  month: ComputedRef<number>
  monthName: ComputedRef<string>
  monthKey: ComputedRef<string>
  navigateMonth: (direction: "prev" | "next") => void
}

export function useCalendar(): UseCalendarReturn {
  const currentDate = ref<Date>(new Date(2025, 6, 1))

  const months: string[] = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ]

  const year = computed<number>(() => currentDate.value.getFullYear())
  const month = computed<number>(() => currentDate.value.getMonth())
  const monthName = computed<string>(() => months[month.value])
  const monthKey = computed<string>(() => `${year.value}-${month.value + 1}`)

  const navigateMonth = (direction: "prev" | "next"): void => {
    const newDate = new Date(currentDate.value)
    if (direction === "prev") {
      newDate.setMonth(newDate.getMonth() - 1)
    } else {
      newDate.setMonth(newDate.getMonth() + 1)
    }
    currentDate.value = newDate
  }

  return {
    currentDate,
    year,
    month,
    monthName,
    monthKey,
    navigateMonth,
  }
}
