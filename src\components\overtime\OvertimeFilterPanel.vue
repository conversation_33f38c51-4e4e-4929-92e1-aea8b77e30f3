<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { useDateFormats } from '@/composables/useDateFormats';
import { statuses } from '@/constants';
import type { Status } from '@/enums';
import { useHeaderActionsStore } from '@/stores/header-actions';
import { FilterIcon, XIcon } from 'lucide-vue-next';
import { NDatePicker, NSelect } from 'naive-ui';
import { computed, h, onMounted, onUnmounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';

interface Props {
  filterStatus: Status | 'all';
  filterDateFrom: string | null;
  filterDateTo: string | null;
}

interface Emits {
  'update:filterStatus': [value: Status | 'all'];
  'update:filterDateFrom': [value: string | null];
  'update:filterDateTo': [value: string | null];
  resetFilters: [];
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();
const headerActionsStore = useHeaderActionsStore();

const { t } = useI18n();
const { pickerConfigs } = useDateFormats();

const showFilters = ref(false);

// Translate status options
const statusOptions = computed(() =>
  statuses.map((status) => ({
    ...status,
    label: status.label,
  })),
);

// Check if any filters are active
const hasActiveFilters = computed(() => {
  return (
    props.filterStatus !== 'all' || props.filterDateFrom || props.filterDateTo
  );
});

// Computed for two-way binding
const localFilterStatus = computed({
  get: () => props.filterStatus,
  set: (value) => emit('update:filterStatus', value),
});

const localFilterDateFrom = computed({
  get: () => props.filterDateFrom,
  set: (value) => emit('update:filterDateFrom', value),
});

const localFilterDateTo = computed({
  get: () => props.filterDateTo,
  set: (value) => emit('update:filterDateTo', value),
});

const resetFilters = () => {
  emit('resetFilters');
};

const toggleFilters = () => {
  showFilters.value = !showFilters.value;
};

const closeFilters = () => {
  showFilters.value = false;
};

onMounted(() => {
  headerActionsStore.set(() =>
    h(
      'Button',
      {
        class:
          'p-2 transition-colors hover:bg-gray-100 cursor-pointer rounded-md',
        onClick: toggleFilters,
      },
      [h(FilterIcon, { class: 'size-5 text-gray-600' })],
    ),
  );
});

onUnmounted(() => {
  headerActionsStore.clear();
});
</script>

<template>
  <div class="w-full">
    <!-- Filter Controls Panel -->
    <transition
      enter-active-class="transition duration-200 ease-out"
      enter-from-class="opacity-0 scale-95"
      enter-to-class="opacity-100 scale-100"
      leave-active-class="transition duration-150 ease-in"
      leave-from-class="opacity-100 scale-100"
      leave-to-class="opacity-0 scale-95"
    >
      <div
        v-if="showFilters"
        class="mb-6 rounded-lg border border-gray-200 bg-white p-4 shadow-sm"
      >
        <!-- Filter Header with Active Indicator, Reset and Close -->
        <div class="mb-4 flex items-center justify-between">
          <div class="flex items-center gap-2">
            <h3 class="text-sm font-medium text-gray-900">
              {{ t('attendance.filters.filter') }}
            </h3>
            <!-- Active filters indicator -->
            <span
              v-if="hasActiveFilters"
              class="inline-flex items-center rounded-full bg-blue-100 px-2 py-0.5 text-xs font-medium text-blue-800"
            >
              {{ t('attendance.filters.active') }}
            </span>
          </div>

          <div class="flex items-center gap-2">
            <!-- Reset filters button -->
            <Button
              v-if="hasActiveFilters"
              variant="outline-destructive"
              size="sm"
              @click="resetFilters"
              class="h-auto px-3 py-1 text-xs"
              :title="t('attendance.filters.clear_filters')"
            >
              {{ t('attendance.filters.clear_filters') }}
            </Button>

            <!-- Close filters button -->
            <Button
              variant="ghost"
              size="sm"
              @click="closeFilters"
              class="h-8 w-8 p-0"
              :title="t('common.close')"
            >
              <XIcon class="h-4 w-4" />
            </Button>
          </div>
        </div>

        <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
          <!-- Date From Filter -->
          <div class="space-y-2">
            <label class="block text-sm font-medium text-gray-700">
              {{ t('attendance.filters.date_from') }}
            </label>
            <n-date-picker
              v-model:formatted-value="localFilterDateFrom"
              type="date"
              :format="pickerConfigs.attendance.date.format"
              :value-format="pickerConfigs.attendance.date.valueFormat"
              :placeholder="t('attendance.filters.select_date_from')"
              clearable
              class="w-full"
              size="small"
            />
          </div>

          <!-- Date To Filter -->
          <div class="space-y-2">
            <label class="block text-sm font-medium text-gray-700">
              {{ t('attendance.filters.date_to') }}
            </label>
            <n-date-picker
              v-model:formatted-value="localFilterDateTo"
              type="date"
              :format="pickerConfigs.attendance.date.format"
              :value-format="pickerConfigs.attendance.date.valueFormat"
              :placeholder="t('attendance.filters.select_date_to')"
              clearable
              class="w-full"
              size="small"
            />
          </div>

          <!-- Status Filter -->
          <div class="col-span-2 space-y-2">
            <label class="block text-sm font-medium text-gray-700">
              {{ t('attendance.filters.status') }}
            </label>
            <n-select
              v-model:value="localFilterStatus"
              :options="statusOptions"
              :placeholder="t('attendance.filters.select_status')"
              clearable
              class="w-full"
              size="small"
            />
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<style scoped>
/* Custom styles for better visual hierarchy */
.n-select {
  --n-border-radius: 6px;
}

.n-date-picker {
  --n-border-radius: 6px;
}
</style>
