<script setup lang="ts">
import { Separator } from '@/components/ui/separator';
import { Skeleton } from '@/components/ui/skeleton';
</script>

<template>
  <div class="rounded-lg border bg-white p-4 shadow-sm">
    <!-- Header skeleton -->
    <div class="mb-3 flex items-start justify-between">
      <div class="flex-1">
        <Skeleton class="mb-2 h-5 w-3/4" />
        <div class="mt-1 flex items-center gap-1">
          <Skeleton class="h-6 w-24 rounded-sm" />
        </div>
      </div>
      <Skeleton class="h-6 w-20 rounded-full" />
    </div>

    <!-- Details skeleton -->
    <div class="space-y-2">
      <div class="flex items-center gap-2">
        <Skeleton class="h-4 w-4 rounded-full" />
        <Skeleton class="h-4 w-32" />
      </div>

      <div class="flex items-center gap-2">
        <Skeleton class="h-4 w-4 rounded-full" />
        <Skeleton class="h-4 w-24" />
      </div>

      <Skeleton class="h-10 w-full" />
    </div>

    <Separator class="my-4 bg-gray-300" />

    <!-- Actions skeleton -->
    <div class="flex gap-2">
      <Skeleton class="h-10 flex-1" />
      <Skeleton class="h-10 w-24" />
    </div>
  </div>
</template>
