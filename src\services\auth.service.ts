import type {
  ForgotPasswordResponse,
  LoginCredentials,
  LoginResponse,
} from '@/interfaces/auth';

import apiClient from '../api/axios-instance';

export const authService = {
  login: (credentials: LoginCredentials) =>
    apiClient.post<LoginResponse>('/auth/login', credentials),

  forgotPassword: (email: string) =>
    apiClient.post<ForgotPasswordResponse>('/auth/forgot-password', { email }),

  logout: () => apiClient.post('/auth/logout'),
};

export default authService;
