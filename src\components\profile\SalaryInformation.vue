<script setup lang="ts">
import {
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
} from '@/components/ui/drawer';
import { Separator } from '@/components/ui/separator';
import { useUserStore } from '@/stores/user';
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();

const userStore = useUserStore();

const detailsList = computed(() => [
  // { label: 'Cost to Company (CTC)', content: userStore.user?.salary || '₫ 0' },
  // { label: 'Payroll Cost Center', content: userStore.user?.payroll_cost_center || '-' },
  // { label: 'Salary Mode', content: userStore.user?.salary_mode || '-' },
  { label: t('salary.bank_name'), content: userStore.user?.issue_bank || '-' },
  {
    label: t('salary.account_number'),
    content: userStore.user?.account_number || '-',
  },
  {
    label: t('salary.account_name'),
    content: userStore.user?.name_account || '-',
  },
  {
    label: t('salary.personal_taxCode'),
    content: userStore.user?.personal_tax_code || '-',
  },
]);
</script>

<template>
  <DrawerContent>
    <DrawerHeader>
      <DrawerTitle class="text-center">{{
        t('profile.salary_information')
      }}</DrawerTitle>
      <DrawerDescription class="hidden" />
    </DrawerHeader>
    <Separator />
    <div class="flex w-full flex-col items-center justify-center gap-4 p-4">
      <div
        v-for="(item, index) in detailsList"
        :key="index"
        class="flex w-full items-center justify-between gap-x-2"
      >
        <div class="text-sm text-gray-600">{{ item.label }}</div>
        <div class="font-medium text-gray-900">{{ item.content }}</div>
      </div>
    </div>
  </DrawerContent>
</template>
