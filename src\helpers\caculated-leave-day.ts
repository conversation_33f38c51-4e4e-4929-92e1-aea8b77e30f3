const calculateLeaveDays = (): number => {
  if (!form.value.start_time || !form.value.end_time || !isLeaveType.value) {
    return 0;
  }

  const startDate = toVietnamDate(form.value.start_time);
  const endDate = toVietnamDate(form.value.end_time);

  if (startDate > endDate) {
    return 0;
  }

  let totalDays = 0;
  const currentDate = new Date(startDate);

  const isSameDay = startDate.getTime() === endDate.getTime();

  while (currentDate <= endDate) {
    const dayOfWeek = currentDate.getDay();
    if (dayOfWeek !== 0 && dayOfWeek !== 6) {
      if (isSameDay) {
        if (startDateSession.value === 'session_1' && endDateSession.value === 'session_2') {
          totalDays += 1;
        } else if (startDateSession.value === 'session_1' && endDateSession.value === 'session_1') {
          totalDays += 0.5;
        } else if (startDateSession.value === 'session_2' && endDateSession.value === 'session_2') {
          totalDays += 0.5;
        }
        break; // Exit loop since it's the same day
      } else if (currentDate.getTime() === startDate.getTime()) {
        // First day
        if (startDateSession.value === 'full_day') {
          totalDays += 1;
        } else {
          totalDays += 0.5;
        }
      } else if (currentDate.getTime() === endDate.getTime()) {
        // Last day
        if (endDateSession.value === 'full_day') {
          totalDays += 1;
        } else {
          totalDays += 0.5;
        }
      } else {
        // Middle days - always full day
        totalDays += 1;
      }
    }
    currentDate.setDate(currentDate.getDate() + 1);
  }

  return totalDays;
};
