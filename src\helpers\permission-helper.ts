import type { NavigationGuardNext, RouteLocationNormalized } from 'vue-router';

import { DEFAULT_LOCALE, type SupportedLocale } from '@/constants/locales';
import { NOT_FOUND } from '@/constants/routes';
import { RoleName } from '@/enums/role';
import { hasPermission } from '@/helpers/staff-helper';
import { useAuthRoleStore } from '@/stores/auth-role';
import { getLocalizedPath } from '@/utils/localized-navigation';
import { useStorage } from '@vueuse/core';

/**
 *Check access based on meta.role
 *@Param To -Route is being accessed
 *@Param From -Route before
 *@Param Next -Navigation function
 */
export const checkPermission = (
  to: RouteLocationNormalized,
  from: RouteLocationNormalized,
  next: NavigationGuardNext,
) => {
  const authRoleStore = useAuthRoleStore();
  const storedLocale = useStorage<SupportedLocale>('locale', DEFAULT_LOCALE);
  const userRole = authRoleStore.roleName || '';

  const requiredRole = to.meta.role as RoleName;

  const notFoundPath = getLocalizedPath(NOT_FOUND, storedLocale.value);

  if (requiredRole && !hasPermission(userRole, requiredRole)) {
    next({ path: notFoundPath });
  }

  next();
};
