import tailwindcss from '@tailwindcss/vite';
import vue from '@vitejs/plugin-vue';
import path from 'path';
import { defineConfig } from 'vite';
import { VitePWA } from 'vite-plugin-pwa';

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    tailwindcss(),
    VitePWA({
      registerType: 'autoUpdate',
      includeAssets: ['images/pwa/sharp-152x152.png'],
      manifest: {
        name: 'Sharp',
        short_name: 'Sharp',
        description: 'Sharp Application',
        theme_color: '#ffffff',
        icons: [
          {
            src: 'images/pwa/sharp-192x192.png',
            sizes: '192x192',
            type: 'image/png',
          },
          {
            src: 'images/pwa/sharp-512x512.png',
            sizes: '512x512',
            type: 'image/png',
          },
          {
            src: 'images/pwa/sharp-512x512.png',
            sizes: '512x512',
            type: 'image/png',
            purpose: 'any maskable',
          },
        ],
        display: 'standalone',
        start_url: '/',
        background_color: '#ffffff',
      },
      workbox: {
        skipWaiting: true,
        clientsClaim: true,
      },
      devOptions: {
        enabled: true,
        type: 'module',
        navigateFallback: 'index.html',
      },
    }),
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  server: {
    port: 5000,
    hmr: {
      overlay: false,
    },
    watch: {
      ignored: ['**/public/firebase-messaging-sw.js'],
    },
  },
});
