<script setup lang="ts">
import OvertimeFilterPanel from '@/components/overtime/OvertimeFilterPanel.vue';
import OvertimeList from '@/components/overtime/OvertimeList.vue';
import OvertimeListSkeleton from '@/components/overtime/OvertimeListSkeleton.vue';
import OvertimeSummaryStats from '@/components/overtime/OvertimeSummaryStats.vue';
import OvertimeSummaryStatsSkeleton from '@/components/overtime/OvertimeSummaryStatsSkeleton.vue';
import { Pagination } from '@/components/ui/pagination';
import { useAttendance } from '@/composables/useAttendance';
import { useOvertimeFilter } from '@/composables/useOvertimeFilter';
import { DEFAULT_TOTAL_ITEMS, DEFAULT_TOTAL_PAGE } from '@/constants';
import { Status } from '@/enums';
import { IonPage } from '@ionic/vue';
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const {
  filterStatus,
  filterDateFrom,
  filterDateTo,
  hasActiveFilters,
  filterParams,
  resetFilters,
  currentPage,
  goToPage,
  nextPage,
  prevPage,
} = useOvertimeFilter();

const { createOvertimeQuery } = useAttendance();
const overtimeQuery = createOvertimeQuery(filterParams);

const overtimeRequests = computed(() => overtimeQuery.data.value?.data || []);
const paginationMeta = computed(() => overtimeQuery.data.value?.pagination);
const totalRecords = computed(
  () => overtimeQuery.data.value?.total || DEFAULT_TOTAL_ITEMS,
);

const totalPages = computed(
  () => paginationMeta.value?.total_pages || DEFAULT_TOTAL_PAGE,
);

const approvedRequests = computed(
  () =>
    overtimeRequests.value.filter((req) => req.status === Status.APPROVED)
      .length,
);
const pendingRequests = computed(
  () =>
    overtimeRequests.value.filter((req) => req.status === Status.PENDING)
      .length,
);
const rejectedRequests = computed(
  () =>
    overtimeRequests.value.filter((req) => req.status === Status.REJECTED)
      .length,
);
const totalHours = computed(() =>
  overtimeRequests.value.reduce(
    (sum, req) => sum + (req.timekeeping_value || 0),
    0,
  ),
);

const handlePrevPage = () => {
  prevPage();
};

const handleNextPage = () => {
  nextPage();
};

const handleGoToPage = (page: number) => {
  goToPage(page);
};
</script>

<template>
  <IonPage>
    <div class="scroll-container flex h-full flex-col items-center gap-y-5 bg-gray-50 px-4 py-6">
      <!-- Filter Panel -->
      <OvertimeFilterPanel v-model:filter-status="filterStatus" v-model:filter-date-from="filterDateFrom"
        v-model:filter-date-to="filterDateTo" @reset-filters="resetFilters" />

      <!-- Loading State -->
      <template v-if="overtimeQuery.isLoading.value">
        <OvertimeSummaryStatsSkeleton />
        <OvertimeListSkeleton :count="5" />
      </template>

      <!-- Error State -->
      <div v-else-if="overtimeQuery.isError.value" class="py-8 text-center">
        <p class="text-red-600">
          {{ t('attendance.overtime.messages.failed_to_load') }}
        </p>
      </div>

      <!-- Loaded State -->
      <template v-else>
        <!-- Summary Stats -->
        <OvertimeSummaryStats :totalRequests="totalRecords" :approvedRequests="approvedRequests"
          :pendingRequests="pendingRequests" :rejectedRequests="rejectedRequests" :totalHours="totalHours" />

        <!-- Overtime Requests List -->
        <OvertimeList :overtimeRequests="overtimeRequests" :loading="overtimeQuery.isLoading.value"
          :hasActiveFilters="hasActiveFilters" @clear-filters="resetFilters" />

        <!-- Pagination -->
        <Pagination :totalPages="totalPages" :currentPage="currentPage" :totalRecords="totalRecords"
          :loading="overtimeQuery.isLoading.value" @prevPage="handlePrevPage" @nextPage="handleNextPage"
          @goToPage="handleGoToPage" />
      </template>
    </div>
  </IonPage>
</template>
