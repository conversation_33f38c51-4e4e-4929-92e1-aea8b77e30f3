import { computed, ref } from 'vue';

import { useLocale } from '@/composables/useLocale';
import { LOGIN } from '@/constants/routes';
import authService from '@/services/auth.service';
import { useAuthRoleStore } from '@/stores/auth-role';
import { getLocalizedPath } from '@/utils/localized-navigation';
import { useQueryClient } from '@tanstack/vue-query';
import VueCookies from 'vue-cookies';
import { useRouter } from 'vue-router';

export function useAccount() {
  const queryClient = useQueryClient();
  const { setRole } = useAuthRoleStore();
  const cookies = VueCookies;
  const isLoading = ref(false);
  const error = ref<string | null>(null);
  const isAuthenticated = computed(() => !!cookies.get('auth-token'));

  const login = async (username: string, password: string) => {
    isLoading.value = true;
    error.value = null;

    try {
      const response = await authService.login({ username, password });

      if (response.data.status) {
        cookies.set('auth-token', response.data.data.token, '30d');
        setRole(
          response.data.data.staff_role,
          response.data.data.staff_role_name,
        );

        queryClient.invalidateQueries({ queryKey: ['user'] });

        return { success: true, message: response.data.message };
      } else {
        error.value = response.data.message;
        return { success: false, message: response.data.message };
      }
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Login failed';
      return { success: false, message: error.value };
    } finally {
      isLoading.value = false;
    }
  };

  const router = useRouter();
  const { locale } = useLocale();

  const logout = async () => {
    isLoading.value = true;

    try {
      await authService.logout();
    } finally {
      cookies.remove('auth-token');

      queryClient.removeQueries({ queryKey: ['user'] });

      const localizedLoginPath = getLocalizedPath(LOGIN, locale.value);

      router.push(localizedLoginPath);

      isLoading.value = false;
    }
  };

  const forgotPassword = async (email: string) => {
    isLoading.value = true;
    error.value = null;

    try {
      const response = await authService.forgotPassword(email);
      return { success: true, message: response.data.message };
    } catch (err: any) {
      error.value =
        err.response?.data?.message || 'Password reset request failed';
      return { success: false, message: error.value };
    } finally {
      isLoading.value = false;
    }
  };

  return {
    isAuthenticated,
    isLoading,
    error,
    login,
    logout,
    forgotPassword,
  };
}
