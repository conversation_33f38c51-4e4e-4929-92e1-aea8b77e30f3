<script setup lang="ts">
import QuickLinks from '@/components/home/<USER>';
import Welcome from '@/components/home/<USER>';
import { useUserStore } from '@/stores/user';
import { IonPage } from '@ionic/vue';
import { onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { toast } from 'vue-sonner';

const userStore = useUserStore();
const { t } = useI18n();

onMounted(async () => {
  try {
    await userStore.getCurrentUser();
  } catch (error) {
    toast.error(t('common.toast.error.load_user'), {
      description:
        error instanceof Error
          ? error.message
          : t('common.toast.error.unknown'),
    });
  }
});
</script>

<template>
  <ion-page class="ion-padding mt-5 !justify-start gap-y-7">
    <Welcome :fullName="userStore.fullName" :isLoading="userStore.isLoading" />
    <QuickLinks :isLoading="userStore.isLoading" />
  </ion-page>
</template>
