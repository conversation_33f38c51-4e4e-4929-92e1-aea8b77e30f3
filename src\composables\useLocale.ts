import type { SupportedLocale } from '@/constants/locales';
import { setLocale } from '@/plugins/i18n';
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';
import { toast } from 'vue-sonner';

export function useLocale() {
  const router = useRouter();
  const i18n = useI18n();

  const locale = computed({
    get: () => i18n.locale.value as SupportedLocale,
    set: (value: SupportedLocale) => {
      i18n.locale.value = value;
    },
  });

  const changeLocale = async (newLocale: SupportedLocale): Promise<void> => {
    if (newLocale === locale.value) {
      return;
    }

    try {
      await setLocale(newLocale);

      updateUrlWithLocale(newLocale);
    } catch (error) {
      toast.error(i18n.t('common.toast.error.set_locale'), {
        description:
          error instanceof Error
            ? error.message
            : i18n.t('common.toast.error.unknown'),
      });
    }
  };

  const updateUrlWithLocale = (locale: SupportedLocale): void => {
    const { fullPath } = router.currentRoute.value;
    const pathWithoutLocale = fullPath.replace(/^\/[^/]+/, '');
    const newPath = `/${locale}${pathWithoutLocale || '/'}`;
    router.push(newPath);
  };

  return {
    locale,
    changeLocale,
    updateUrlWithLocale,
  };
}
