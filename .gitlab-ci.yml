stages:
  - build
  - deploy

variables:
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: ""

build:
  stage: build
  image: docker:latest
  services:
    - docker:dind
  before_script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
  script:
    - docker build -t $CI_REGISTRY_IMAGE:sharp .
    - docker push $CI_REGISTRY_IMAGE:sharp
  only:
    - sharp-pwa

deploy_to_vps:
  stage: deploy
  image: alpine:latest
  before_script:
    - apk add --no-cache openssh-client sshpass rsync
  script:
    - echo "Testing SSH connection..."
    - sshpass -p "$VPS_PASSWORD" ssh -o StrictHostKeyChecking=no $VPS_USER@$VPS_HOST "cd /root/sharp/pwa && chmod +x deploy.sh && ./deploy.sh" || (echo "SSH connection failed"; exit 1)
  environment:
    name: production
  only:
    - sharp-pwa
