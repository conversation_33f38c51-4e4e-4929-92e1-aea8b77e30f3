import type { Directive } from 'vue';

interface ClickOutsideElement extends HTMLElement {
  _clickOutside?: (event: MouseEvent) => void;
}

export const clickOutside: Directive = {
  mounted(el: ClickOutsideElement, binding) {
    el._clickOutside = (event: MouseEvent) => {
      if (!(el === event.target || el.contains(event.target as Node))) {
        binding.value(event);
      }
    };
    document.addEventListener('click', el._clickOutside as EventListener);
  },
  unmounted(el: ClickOutsideElement) {
    if (el._clickOutside) {
      document.removeEventListener('click', el._clickOutside as EventListener);
      delete el._clickOutside;
    }
  },
};
