<script setup lang="ts">
import { useLocale } from '@/composables/useLocale';
import { HOME } from '@/constants/routes';
import { useHeaderActionsStore } from '@/stores/header-actions';
import { useHistoryStore } from '@/stores/history';
import { useLayoutStore } from '@/stores/title-layout';
import { getLocalizedPath } from '@/utils/localized-navigation';
import { normalizePath } from '@/utils/normalize-path';
import {
  IonContent,
  IonHeader,
  IonIcon,
  IonPage,
  IonRouterOutlet,
  IonTitle,
  IonToolbar,
} from '@ionic/vue';
import { arrowBackOutline } from 'ionicons/icons';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';

const router = useRouter();
const route = useRoute();
const layoutTitle = useLayoutStore();
const historyStore = useHistoryStore();
const { locale } = useLocale();
const headerActionsStore = useHeaderActionsStore();
const { t } = useI18n();

const handleBack = () => {
  const prev = historyStore.getPreviousUrl(normalizePath(route.fullPath));

  const newPath = getLocalizedPath(prev || HOME, locale.value);

  router.push({ path: newPath, replace: true });
  historyStore.clearHistory();
};
</script>

<template>
  <ion-page>
    <ion-header :translucent="true" class="custom-toolbar">
      <ion-toolbar class="px-3">
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-3">
            <button
              @click="handleBack"
              class="flex cursor-pointer items-center justify-center"
            >
              <ion-icon
                :icon="arrowBackOutline"
                class="size-6 [--ionicon-stroke-width:32px]"
              />
            </button>
            <ion-title class="!font-semibold">
              {{ t(layoutTitle.pageTitle) }}
            </ion-title>
          </div>

          <!-- Content Actions  -->
          <component
            :is="headerActionsStore.actions"
            v-if="headerActionsStore.actions"
          />
        </div>
      </ion-toolbar>
    </ion-header>
    <ion-content>
      <ion-router-outlet />
    </ion-content>
  </ion-page>
</template>

<style scoped>
.custom-toolbar {
  --box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}
</style>
