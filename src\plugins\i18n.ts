import { DEFAULT_LOCALE, type SupportedLocale } from '@/constants/locales';
import { createI18n } from 'vue-i18n';
import { toast } from 'vue-sonner';

const modules = [
  'attendance',
  'auth',
  'common',
  'expense',
  'home',
  'leaves',
  'notification',
  'profile',
  'salary',
  'pwa',
  'leave-card',
  'management',
];

const loadedLanguages = new Set<SupportedLocale>();

async function loadLocaleMessages(locale: SupportedLocale) {
  const messages: Record<string, any> = {};

  await Promise.all(
    modules.map(async (module) => {
      try {
        const moduleMessages = await import(
          `../locales/${locale}/${module}.json`
        );
        messages[module] = moduleMessages.default[module];
      } catch (error) {
        toast.error(`Failed to load ${module} locale`, {
          description: error instanceof Error ? error.message : String(error),
        });
      }
    }),
  );

  return messages;
}

const i18n = createI18n({
  legacy: false,
  locale: 'en',
  fallbackLocale: 'en',
  messages: {},
  missingWarn: false,
  fallbackWarn: false,
});

export async function setLocale(locale: SupportedLocale) {
  if (!loadedLanguages.has(locale)) {
    try {
      const messages = await loadLocaleMessages(locale);
      i18n.global.setLocaleMessage(locale, messages);
      loadedLanguages.add(locale);
    } catch (error) {
      toast.error(i18n.global.t('common.toast.error.set_locale'), {
        description:
          error instanceof Error
            ? error.message
            : i18n.global.t('common.toast.error.unknown'),
      });
    }
  }

  i18n.global.locale.value = locale;
  document.documentElement.lang = locale;
}

setLocale(DEFAULT_LOCALE);

export default i18n;
