<script setup lang="ts">
import { useLeaveRequests } from '@/composables/useLeaveRequests';
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { Skeleton } from '../ui/skeleton';

const { statisticsQuery } = useLeaveRequests();

const isLoading = computed(() => statisticsQuery.isLoading.value);
const stats = computed(() => {
  const data = statisticsQuery.data.value;
  return data;
});

const { t } = useI18n();
</script>

<template>
  <div class="grid w-full grid-cols-4 items-center gap-2.5">
    <template v-if="!isLoading && stats">
      <div class="flex flex-col items-center gap-1 rounded-md border border-gray-200 px-2.5 py-2">
        <span class="text-c-secondary text-xl font-bold">{{ stats.total_requests }}</span>
        <span class="text-xs text-gray-500">{{ t('leaves.statistics.total') }}</span>
      </div>
      <div class="flex flex-col items-center gap-1 rounded-md border border-gray-200 px-2.5 py-2">
        <span class="text-xl font-bold text-emerald-500">{{ stats.total_approves }}</span>
        <span class="text-xs text-gray-500">{{ t('leaves.statistics.approved') }}</span>
      </div>
      <div class="flex flex-col items-center gap-1 rounded-md border border-gray-200 px-2.5 py-2">
        <span class="text-xl font-bold text-amber-500">{{ stats.total_pendings }}</span>
        <span class="text-xs text-gray-500">{{ t('leaves.statistics.pending') }}</span>
      </div>
      <div class="flex flex-col items-center gap-1 rounded-md border border-gray-200 px-2.5 py-2">
        <span class="text-c-primary text-xl font-bold">{{ stats.total_rejecteds }}</span>
        <span class="text-xs text-gray-500">{{ t('leaves.statistics.rejected') }}</span>
      </div>
    </template>

    <template v-else>
      <!-- loading skeleton -->
      <div v-for="n in 4" :key="n"
        class="flex flex-col items-center gap-1 rounded-md border border-gray-200 px-2.5 py-2">
        <Skeleton class="h-6 w-8" />
        <Skeleton class="h-3 w-16" />
      </div>
    </template>
  </div>
</template>
