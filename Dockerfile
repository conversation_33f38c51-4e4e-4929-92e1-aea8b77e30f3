# Multi-stage Dockerfile for Vue.js application with Ionic and PWA support

# Stage 1: Build stage
FROM node:22-alpine AS builder

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./
COPY bunfig.toml ./

# Install all dependencies first (including dev dependencies for build)
RUN npm ci --silent --ignore-scripts

# Copy source code
COPY . .

# Create .env file with Firebase configuration for build
RUN echo "VITE_FIREBASE_API_KEY=AIzaSyCacJADCxhOAbsKXoFNavk8vjBtLmsm-fc" > .env && \
    echo "VITE_FIREBASE_AUTH_DOMAIN=sharp-pwa.firebaseapp.com" >> .env && \
    echo "VITE_FIREBASE_PROJECT_ID=sharp-pwa" >> .env && \
    echo "VITE_FIREBASE_STORAGE_BUCKET=sharp-pwa.firebasestorage.app" >> .env && \
    echo "VITE_FIREBASE_MESSAGING_SENDER_ID=747568647844" >> .env && \
    echo "VITE_FIREBASE_APP_ID=1:747568647844:web:0a2e1da87dc1a78998a031" >> .env && \
    echo "VITE_FIREBASE_MEASUREMENT_ID=G-DKZ9W6XVRW" >> .env && \
    echo "VITE_FIREBASE_VAPID_KEY=BCRe8DnpEUxa4h__wmuMT-wpjfRT0gkWn8B6K57iTaqOZvV_EFvYluwnHJhkSK0cwSy-DmAqWomKWlk9ZQLWBs8" >> .env && \
    echo "VITE_API_BASE_URL=localhost:8080/flutex_admin_api" >> .env && \
    echo "VITE_PUSHER_APP_KEY=a30e473765cbdf462ffb" >> .env && \
    echo "VITE_PUSHER_CLUSTER=ap1" >> .env && \
    echo "VITE_SHOW_DEVTOOLS=false" >> .env

# Build the application
RUN npm run build

# Stage 2: Production stage with nginx
FROM nginx:alpine AS production

# Install additional packages for better nginx performance
RUN apk add --no-cache \
    curl \
    tzdata

# Remove default nginx configuration
RUN rm -rf /etc/nginx/conf.d/default.conf

# Copy custom nginx configuration
COPY nginx/nginx.conf /etc/nginx/nginx.conf
COPY nginx/default.conf /etc/nginx/conf.d/default.conf

# Copy built application from builder stage
COPY --from=builder /app/dist /usr/share/nginx/html

# Create nginx cache directories
RUN mkdir -p /var/cache/nginx/client_temp \
    /var/cache/nginx/proxy_temp \
    /var/cache/nginx/fastcgi_temp \
    /var/cache/nginx/uwsgi_temp \
    /var/cache/nginx/scgi_temp

# Set proper permissions
RUN chown -R nginx:nginx /var/cache/nginx \
    && chown -R nginx:nginx /usr/share/nginx/html \
    && chmod -R 755 /usr/share/nginx/html

# Add health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:80/ || exit 1

# Expose port
EXPOSE 80

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
