<script setup lang="ts">
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { NBadge } from 'naive-ui';
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';

interface Props {
  modelValue: string;
  pendingCount: number;
}

interface Emits {
  (e: 'update:modelValue', value: string): void;
}

defineEmits<Emits>();

const { t } = useI18n();

const statusFilterOptions = computed(() => [
  {
    label: t('management.requests.status.all'),
    value: 'all',
  },
  {
    label: t('management.requests.status.pending'),
    value: 'pending',
    badge: props.pendingCount,
  },
  {
    label: t('management.requests.status.approved'),
    value: 'approved',
  },
  {
    label: t('management.requests.status.rejected'),
    value: 'rejected',
  },
]);

const props = defineProps<Props>();
</script>

<template>
  <div class="mb-6 flex justify-center px-3">
    <Tabs
      :model-value="modelValue"
      default-value="pending"
      class="w-full max-w-md"
      @update:model-value="$emit('update:modelValue', String($event))"
    >
      <TabsList class="grid w-full grid-cols-4">
        <TabsTrigger
          v-for="option in statusFilterOptions"
          :key="option.value"
          :value="option.value"
          class="relative"
        >
          <div class="flex items-center gap-2">
            {{ option.label }}
            <n-badge
              v-if="option.badge && option.badge > 0"
              :value="option.badge"
              :max="99"
              size="small"
            />
          </div>
        </TabsTrigger>
      </TabsList>
    </Tabs>
  </div>
</template>
