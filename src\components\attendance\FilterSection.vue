<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { useDateFormats } from '@/composables/useDateFormats';
import { format } from 'date-fns';
import { X } from 'lucide-vue-next';
import { NDatePicker } from 'naive-ui';
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const { dateFormats } = useDateFormats();

const props = defineProps<{
  filterFrom: string;
  filterTo: string;
  showFilter: boolean;
  onToggleFilter: () => void;
  onCloseFilter: () => void;
}>();

const emit = defineEmits<{
  (e: 'update:filterFrom', value: string): void;
  (e: 'update:filterTo', value: string): void;
  (e: 'clearFilter'): void;
}>();

const parseDate = (dateStr: string): number | null => {
  if (!dateStr) return null;

  const [day, month, year] = dateStr.split('/');
  const parsedDate = new Date(Number(year), Number(month) - 1, Number(day));
  return isNaN(parsedDate.getTime()) ? null : parsedDate.getTime();
};

const localFilterFrom = ref<number | null>(parseDate(props.filterFrom));
const localFilterTo = ref<number | null>(parseDate(props.filterTo));

watch(localFilterFrom, (newVal) => {
  const formatted = newVal ? format(new Date(newVal), 'dd/MM/yyyy') : '';
  emit('update:filterFrom', formatted);
});

watch(localFilterTo, (newVal) => {
  const formatted = newVal ? format(new Date(newVal), 'dd/MM/yyyy') : '';
  emit('update:filterTo', formatted);
});

const clearFilters = () => {
  localFilterFrom.value = null;
  localFilterTo.value = null;
  emit('clearFilter');
};
</script>

<template>
  <transition name="slide-fade">
    <div
      v-if="props.showFilter"
      class="space-y-3 border-b border-gray-200 bg-white px-4 py-3"
    >
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold">
          {{ t('attendance.filter.title') }}
        </h3>
        <div class="flex items-center gap-x-2">
          <button
            class="hover:text-c-primary hover:bg-c-primary/20 cursor-pointer rounded p-0.5 text-gray-600"
            @click="props.onCloseFilter"
          >
            <X class="size-5" />
          </button>
        </div>
      </div>
      <div class="flex items-center space-x-3">
        <div class="flex-1">
          <Label class="mb-1 block text-sm font-medium text-gray-700">
            {{ t('attendance.filter.from_date') }}
          </Label>
          <NDatePicker
            v-model:value="localFilterFrom"
            type="date"
            :format="dateFormats.date.display"
            :placeholder="dateFormats.date.display"
          />
        </div>
        <div class="flex-1">
          <Label class="mb-1 block text-sm font-medium text-gray-700">
            {{ t('attendance.filter.to_date') }}
          </Label>
          <NDatePicker
            v-model:value="localFilterTo"
            type="date"
            :format="dateFormats.date.display"
            :placeholder="dateFormats.date.display"
          />
        </div>
      </div>
      <Button
        variant="outline-destructive"
        size="lg"
        @click="clearFilters"
        class="w-full"
      >
        {{ t('attendance.filter.clear_filter') }}
      </Button>
    </div>
  </transition>
</template>
