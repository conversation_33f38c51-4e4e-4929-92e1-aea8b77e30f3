<script setup lang="ts">
import { Button } from '@/components/ui/button';
import {
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
} from '@/components/ui/drawer';
import { Separator } from '@/components/ui/separator';
import { useLocale } from '@/composables/useLocale';
import {
  formatLeaveType,
  getLeaveTypeClass,
  getLeaveTypeIcon,
  getStatusClass,
  getStatusIcon,
  getStatusText,
} from '@/constants/leave-history';
import { Status } from '@/enums';
import type { Leave } from '@/interfaces/leave';
import { formatDateLocalized } from '@/utils/format';
import { getLocalizedPath } from '@/utils/localized-navigation';
import { Paperclip, UserCircle, X } from 'lucide-vue-next';
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';

interface Props {
  item: Leave;
}


interface Emits {
  (e: 'close'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();
const { t } = useI18n();
const { locale } = useLocale();
const router = useRouter();

const canEdit = computed(() => {
  return (
    props.item?.can_update &&
    (props.item?.status === Status.PENDING ||
      props.item?.status === Status.REJECTED)
  );
});

const handleEdit = async () => {
  if (canEdit.value) {
    const editPath = getLocalizedPath(
      `/leaves/${props.item.id}/edit`,
      locale.value,
    );

    emit('close');

    router.push(editPath);
  }
};
</script>

<template>
  <DrawerContent>
    <div class="scroll-container w-full space-y-2.5">
      <DrawerHeader>
        <div class="flex items-center justify-between">
          <DrawerTitle class="text-lg">{{ props.item.subject }}</DrawerTitle>
          <DrawerClose as-child>
            <button
              class="hover:text-destructive hover:bg-destructive/20 cursor-pointer rounded-md p-1.5 text-gray-600">
              <X class="size-5" stroke-width="2.5" />
            </button>
          </DrawerClose>
        </div>

        <DrawerDescription>
          <div class="flex items-center gap-3">
            <div class="flex items-center gap-2 rounded-full px-3 py-1.5 text-sm font-medium"
              :class="getStatusClass(props.item?.status)">
              <component :is="getStatusIcon(props.item?.status)" class="h-4 w-4" />
              {{ getStatusText(props.item.status) }}
            </div>

            <div class="flex items-center gap-2 rounded-lg px-3 py-1.5 text-sm font-medium"
              :class="getLeaveTypeClass(props.item?.type_of_leave)">
              <component :is="getLeaveTypeIcon(props.item?.type_of_leave)" class="h-4 w-4" />
              {{ formatLeaveType(props.item?.type_of_leave) }}
            </div>
          </div>
        </DrawerDescription>
      </DrawerHeader>

      <div class="space-y-6 px-4">
        <div class="rounded-lg bg-gray-100 p-3.5">
          <h3 class="mb-3 text-lg font-medium text-gray-900">
            {{ t('leaves.detail.date_information') }}
          </h3>
          <div class="space-y-3">
            <div class="flex items-center justify-between">
              <span class="text-gray-600">{{
                t('leaves.detail.start_date')
                }}</span>
              <span class="font-medium">{{
                formatDateLocalized(props.item?.start_time, locale)
                }}</span>
            </div>
            <div class="flex items-center justify-between">
              <span class="text-gray-600">{{
                t('leaves.detail.end_date')
                }}</span>
              <span class="font-medium">{{
                formatDateLocalized(props.item?.end_time, locale)
                }}</span>
            </div>
            <div v-if="props.item?.number_of_leaving_day" class="flex items-center justify-between">
              <span class="text-gray-600">{{
                t('leaves.detail.duration')
                }}</span>
              <span class="font-medium">
                {{
                  props.item.number_of_leaving_day > 1
                    ? t('leaves.common.count_days', {
                      count: props.item.number_of_leaving_day,
                    })
                    : t('leaves.common.count_day', {
                      count: props.item.number_of_leaving_day,
                    })
                }}
              </span>
            </div>
          </div>
        </div>

        <div v-if="
          props.item?.group_members && props.item.group_members.length > 0
        ">
          <h3 class="mb-3 font-medium text-gray-900">
            {{ t('leaves.detail.team_members') }} ({{
              props.item.group_members.length
            }})
          </h3>
          <div class="grid grid-cols-1 gap-3 sm:grid-cols-2">
            <div v-for="member in props.item.group_members" :key="member.id"
              class="flex items-center gap-3 rounded-lg bg-gray-50 p-3">
              <UserCircle class="h-8 w-8 text-gray-400" />
              <div>
                <p class="text-sm font-medium text-gray-900">
                  {{ member.full_name }}
                </p>
                <p class="text-xs text-gray-500">{{ member.staff_identifi }}</p>
              </div>
            </div>
          </div>
        </div>

        <div v-if="props.item?.reason">
          <h3 class="mb-2 text-lg font-medium text-gray-900">
            {{ t('leaves.detail.reason') }}
          </h3>
          <p class="rounded-lg bg-gray-100 p-4 text-gray-700">
            {{ props.item.reason }}
          </p>
        </div>

        <div v-if="props.item?.attachment" class="bg-c-secondary/10 rounded-lg p-4">
          <h3 class="mb-2 text-lg font-medium text-gray-900">
            {{ t('leaves.detail.attachment') }}
          </h3>
          <div class="flex items-center gap-3">
            <div class="flex h-10 w-10 items-center justify-center rounded-lg bg-blue-100">
              <Paperclip class="h-5 w-5 text-blue-600" />
            </div>
            <div class="flex-1">
              <p class="font-medium text-gray-900">
                {{ t('leaves.detail.attachment_file') }}
              </p>
            </div>
            <button class="font-medium text-blue-600 hover:text-blue-700">
              {{ t('leaves.detail.download') }}
            </button>
          </div>
        </div>

        <div>
          <h3 class="mb-3 font-medium text-gray-900">
            {{ t('leaves.detail.timeline.title') }}
          </h3>
          <div class="space-y-3">
            <div class="flex gap-3">
              <div class="mt-2 h-2 w-2 rounded-full bg-blue-500"></div>
              <div class="flex-1">
                <p class="text-sm font-medium text-gray-900">
                  {{ t('leaves.detail.timeline.submitted') }}
                </p>
                <p class="text-xs text-gray-500">
                  {{ formatDateLocalized(props.item?.start_time, locale) }}
                </p>
              </div>
            </div>
            <div v-if="props.item?.status === Status.APPROVED" class="flex gap-3">
              <div class="mt-2 h-2 w-2 rounded-full bg-green-500"></div>
              <div class="flex-1">
                <p class="text-sm font-medium text-gray-900">
                  {{ t('leaves.detail.timeline.approved') }}
                </p>
                <p class="text-xs text-gray-500">
                  {{ formatDateLocalized(props.item?.end_time, locale) }}
                </p>
              </div>
            </div>
            <div v-if="props.item?.status === Status.REJECTED" class="flex gap-3">
              <div class="mt-2 h-2 w-2 rounded-full bg-red-500"></div>
              <div class="flex-1">
                <p class="text-sm font-medium text-gray-900">
                  {{ t('leaves.detail.timeline.rejected') }}
                </p>
                <p class="text-xs text-gray-500">
                  {{ formatDateLocalized(props.item?.end_time, locale) }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <Separator class="my-5" />

    <DrawerFooter class="pt-0">
      <div class="flex items-center gap-x-7">
        <Button v-if="canEdit" size="lg" class="flex-1" @click="handleEdit">
          {{ t('leaves.detail.edit') }}
        </Button>
        <DrawerClose as-child>
          <Button variant="outline" size="lg" :class="canEdit ? 'flex-1' : 'w-full'">
            {{ t('leaves.detail.close') }}
          </Button>
        </DrawerClose>
      </div>
    </DrawerFooter>
  </DrawerContent>
</template>
