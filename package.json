{"name": "package.json", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint:oxlint": "oxlint . --fix -D correctness --ignore-path .gitignore", "lint:eslint": "eslint . --fix", "lint": "eslint .", "format": "prettier --write src/", "prepare": "husky", "check-comment": "chmod +x ./check.sh && ./check.sh"}, "dependencies": {"@ionic/vue": "^8.5.6", "@ionic/vue-router": "^8.5.6", "@tailwindcss/vite": "^4.1.4", "@tanstack/vue-query": "^5.74.5", "@tanstack/vue-query-devtools": "^5.74.5", "@types/lodash.debounce": "^4.0.9", "@vueuse/core": "^13.4.0", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "firebase": "^11.8.1", "ionicons": "^7.4.0", "lodash.debounce": "^4.0.8", "lucide-vue-next": "^0.507.0", "naive-ui": "^2.41.0", "pinia": "^3.0.2", "pinia-plugin-persistedstate": "^4.3.0", "pusher-js": "^8.4.0", "reka-ui": "^2.3.1", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.4", "tw-animate-css": "^1.2.9", "vaul-vue": "^0.4.1", "vee-validate": "^4.15.0", "vue": "^3.5.13", "vue-cookies": "^1.8.6", "vue-i18n": "^11.1.3", "vue-router": "^4.5.0", "vue-sonner": "^2.0.0", "zod": "^3.24.3"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@tsconfig/node22": "^22.0.1", "@types/node": "^22.15.12", "@vitejs/plugin-vue": "^5.2.3", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "@vue/tsconfig": "^0.7.0", "dayjs": "^1.11.13", "eslint": "^9.22.0", "eslint-plugin-oxlint": "^0.16.0", "eslint-plugin-vue": "~10.0.0", "husky": "^9.1.7", "jiti": "^2.4.2", "npm-run-all2": "^7.0.2", "oxlint": "^0.16.0", "prettier": "3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "typescript": "~5.8.0", "vite": "^6.2.4", "vite-plugin-pwa": "^1.0.0", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.8"}}