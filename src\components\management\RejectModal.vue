<script setup lang="ts">
import { NInput, NModal } from 'naive-ui';
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';

interface Props {
  show: boolean;
}

interface Emits {
  (e: 'update:show', value: boolean): void;
  (e: 'confirm', reason: string): void;
  (e: 'cancel'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { t } = useI18n();
const rejectionReason = ref('');

watch(
  () => props.show,
  (newValue) => {
    if (newValue) {
      rejectionReason.value = '';
    }
  },
);

const handleConfirm = () => {
  if (rejectionReason.value.trim()) {
    emit('confirm', rejectionReason.value.trim());
  }
};

const handleCancel = () => {
  emit('cancel');
};
</script>

<template>
  <n-modal
    :show="show"
    preset="dialog"
    :title="t('management.requests.reject_modal.title')"
    :positive-text="t('management.requests.reject_modal.confirm')"
    :negative-text="t('management.requests.reject_modal.cancel')"
    @update:show="$emit('update:show', $event)"
    @positive-click="handleConfirm"
    @negative-click="handleCancel"
  >
    <p class="mb-4 text-sm text-gray-600">
      {{ t('management.requests.reject_modal.description') }}
    </p>
    <n-input
      v-model:value="rejectionReason"
      type="textarea"
      :placeholder="t('management.requests.reject_modal.placeholder')"
      :autosize="{ minRows: 4, maxRows: 6 }"
    />
  </n-modal>
</template>
