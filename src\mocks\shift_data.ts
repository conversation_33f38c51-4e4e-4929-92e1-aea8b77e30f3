import type { RawShift, ShiftDefinition, ShiftType } from "../interfaces/calendar"

export const shiftDefinitions: { [key: string]: ShiftDefinition } = {
  A1: { name: "Day Shift", start_time: "07:00", end_time: "15:00", type: "Day" },
  A2: { name: "Day Shift 1", start_time: "06:00", end_time: "14:00", type: "Day" },
  B1: { name: "Night Shift", start_time: "21:00", end_time: "05:00", type: "Night" },
  B2: { name: "Day Shift 2", start_time: "14:00", end_time: "22:00", type: "Day" },
  B4: { name: "Night Shift 2", start_time: "22:00", end_time: "06:00", type: "Night" },
  A5: { name: "Day Shift", start_time: "06:00", end_time: "18:00", type: "Day" },
  B5: { name: "Pre-Overtime Night Shift", start_time: "18:00", end_time: "06:00", type: "Night" },
  CAA1: { name: "Day Shift", start_time: "07:00", end_time: "19:00", type: "Day" },
  CAB1: { name: "Night Shift", start_time: "19:00", end_time: "07:00", type: "Night" },
  A6: { name: "New Day Shift", start_time: "07:00", end_time: "15:10", type: "Day" },
  A7: { name: "New Day Shift 1", start_time: "06:00", end_time: "14:10", type: "Day" },
  A9: { name: "New Day Shift", start_time: "10:00", end_time: "18:10", type: "Day" },
  B3: { name: "New Night Shift", start_time: "21:00", end_time: "05:10", type: "Night" },
  B7: { name: "New Day Shift 2", start_time: "14:00", end_time: "22:10", type: "Day" },
  B9: { name: "New Night Shift 2", start_time: "22:00", end_time: "06:10", type: "Night" },
  BB: { name: "SMT Night Shift", start_time: "19:00", end_time: "03:10", type: "Night" },
  CM1: { name: "CM Day Shift 1", start_time: "07:30", end_time: "15:40", type: "CM" },
  CM2: { name: "CM Day Shift 2", start_time: "08:30", end_time: "16:40", type: "CM" },
  CM3: { name: "CM Day Shift 3", start_time: "09:50", end_time: "18:00", type: "CM" },
  CMA7: { name: "CM Day Shift A7", start_time: "06:00", end_time: "14:10", type: "CM" },
  HA: { name: "Direct Admin Shift", start_time: "08:00", end_time: "16:40", type: "Admin" },
  HO: { name: "Indirect Admin Shift", start_time: "08:00", end_time: "16:50", type: "Admin" },
  HS: { name: "SAS Admin Shift", start_time: "06:45", end_time: "19:25", type: "Admin" },
  TSHC1: { name: "Direct Maternity Early Leave", start_time: "08:00", end_time: "15:40", type: "Maternity" },
  TSHC2: { name: "Direct Maternity Late Arrival", start_time: "09:00", end_time: "16:40", type: "Maternity" },
  TSHO1: { name: "Indirect Maternity Early Leave", start_time: "08:00", end_time: "15:50", type: "Maternity" },
  TSHO2: { name: "Indirect Maternity Late Arrival", start_time: "09:00", end_time: "16:50", type: "Maternity" },
}

export const scheduleData: { [key: string]: RawShift[] } = {
  "2025-08-7": [{ code: "A9" }],
  "2025-08-1": [{ code: "CM2" }],
  "2025-08-3": [{ code: "HS" }],
  "2025-08-01": [{ code: "A1" }, { code: "B2" }],
  "2025-08-02": [{ code: "B1" }],
  "2025-07-03": [{ code: "A5" }],
  "2025-07-04": [{ code: "B5" }],
  "2025-07-05": [{ code: "CM3" }],
  "2025-07-06": [{ code: "HO" }],
  "2025-07-07": [{ code: "TSHO1" }],
  "2025-07-08": [{ code: "A7" }, { code: "B9" }],
  "2025-07-09": [{ code: "BB" }],
  "2025-07-10": [{ code: "CMA7" }],
  "2025-07-11": [{ code: "TSHC2" }],
  "2025-07-12": [{ code: "TSHO2" }],
  "2025-07-13": [{ code: "A1" }, { code: "HA" }],
  "2025-08-10": [{ code: "B1" }, { code: "CM1" }],
  "2025-07-15": [{ code: "A2" }, { code: "B2" }, { code: "HS" }],
  "2025-07-20": [{ code: "A6" }],
  // New data for May 2025
  "2025-05-01": [{ code: "A1" }],
  "2025-05-05": [{ code: "B1" }, { code: "A2" }],
  "2025-05-10": [{ code: "HA" }],
  "2025-05-15": [{ code: "CM1" }, { code: "B2" }],
  "2025-05-20": [{ code: "A5" }],
  "2025-05-25": [{ code: "TSHC1" }],
  "2025-05-30": [{ code: "CAA1" }, { code: "B5" }],
  // New data for June 2025
  "2025-06-03": [{ code: "A6" }],
  "2025-06-07": [{ code: "B3" }, { code: "A7" }],
  "2025-06-12": [{ code: "HO" }],
  "2025-06-18": [{ code: "CM2" }],
  "2025-06-22": [{ code: "B7" }, { code: "CMA7" }],
  "2025-06-28": [{ code: "HS" }],
}

export const attendanceData: { [key: string]: "present" | "absent" | "half_day" | "on_leave" } = {
  "2025-05-01": "present",
  "2025-05-05": "absent",
  "2025-05-10": "present",
  "2025-05-15": "half_day",
  "2025-05-20": "on_leave",
  "2025-06-03": "present",
  "2025-06-07": "absent",
  "2025-06-12": "present",
  "2025-06-18": "half_day",
  "2025-06-22": "on_leave",
}

export const allShifts: ShiftType[] = [
  { code: "A1", description: "Làm việc ca ngày", time: "07:00 - 15:00" },
  { code: "A2", description: "Làm việc ca ngày 1", time: "06:00 - 14:00" },
  { code: "B1", description: "Làm việc ca đêm", time: "21:00 - 05:00" },
  { code: "B2", description: "Làm việc ca ngày 2", time: "14:00 - 22:00" },
  { code: "B4", description: "Làm việc ca đêm 2", time: "22:00 - 06:00" },
  { code: "A5", description: "Làm việc ca ngày", time: "06:00 - 18:00" },
  { code: "B5", description: "Làm việc ca đêm tc trước", time: "18:00 - 06:00" },
  { code: "CAA1", description: "Làm việc ca ngày", time: "07:00 - 19:00" },
  { code: "CAB1", description: "Làm việc ca đêm", time: "19:00 - 07:00" },
  { code: "A6", description: "Làm việc ca ngày mới", time: "07:00 - 15:10" },
  { code: "A7", description: "Làm việc ca ngày mới 1", time: "06:00 - 14:10" },
  { code: "A9", description: "Làm việc ca ngày mới", time: "10:00 - 18:10" },
  { code: "B3", description: "Làm việc ca đêm mới", time: "21:00 - 05:10" },
  { code: "B7", description: "Làm việc ca ngày mới 2", time: "14:00 - 22:10" },
  { code: "B9", description: "Làm việc ca đêm mới 2", time: "22:00 - 06:10" },
  { code: "BB", description: "Làm việc ca đêm smt", time: "19:00 - 03:10" },
  { code: "CAA6", description: "Làm việc ca ngày mới", time: "07:00 - 19:10" },
  { code: "CAA7", description: "Làm việc ca ngày mới", time: "06:00 - 18:10" },
  { code: "CAB3", description: "Làm việc ca đêm mới", time: "19:00 - 07:10" },
  { code: "CAB9", description: "Làm việc ca đêm tăng ca trước mới", time: "18:00 - 06:10" },
  { code: "CM1", description: "Ca ngày CM", time: "07:30 - 15:40" },
  { code: "CM2", description: "Ca ngày CM", time: "08:30 - 16:40" },
  { code: "CM3", description: "Ca ngày CM", time: "09:50 - 18:00" },
  { code: "CMA7", description: "Ca ngày CM", time: "06:00 - 14:10" },
  { code: "HA", description: "Làm việc ca hành chánh trực tiếp", time: "08:00 - 16:40" },
  { code: "HO", description: "Làm việc ca hành chính gián tiếp", time: "08:00 - 16:50" },
  { code: "HS", description: "Làm việc ca hành chính trực tiếp SAS", time: "06:45 - 19:25" },
  { code: "TSHC1", description: "Chế độ thai sản hành chính trực tiếp về sớm", time: "08:00 - 15:40" },
  { code: "TSHC2", description: "Chế độ thai sản hành chính trực tiếp đi trễ", time: "09:00 - 16:40" },
  { code: "TSHO1", description: "Chế độ thai sản hành chính gián tiếp về sớm", time: "08:00 - 15:50" },
  { code: "TSHO2", description: "Chế độ thai sản hành chính gián tiếp đi trễ", time: "09:00 - 16:50" },
  { code: "WA1", description: "Làm việc ngày nghỉ ca ngày", time: "07:00 - 15:00" },
  { code: "WA2", description: "Làm việc ngày nghỉ ca ngày", time: "06:00 - 14:00" },
  { code: "WB1", description: "Làm việc ngày nghỉ ca đêm", time: "21:00 - 05:00" },
  { code: "WB2", description: "Làm việc ngày nghỉ ca ngày", time: "14:00 - 22:00" },
  { code: "WB4", description: "Làm việc ngày nghỉ ca đêm", time: "22:00 - 06:00" },
  { code: "WB5", description: "Làm việc ngày nghỉ ca đêm tăng ca trước", time: "18:00 - 06:00" },
  { code: "WB6", description: "Làm việc ngày nghỉ ca đêm", time: "18:00 - 06:00" },
  { code: "WCAA1", description: "Làm việc ngày nghỉ ca ngày", time: "07:00 - 19:00" },
  { code: "WCAB1", description: "Làm việc ngày nghỉ ca đêm", time: "19:00 - 07:00" },
  { code: "WCM1", description: "Làm việc ngày nghỉ ca ngày CM1", time: "07:30 - 15:30" },
  { code: "WCM2", description: "Làm việc ngày nghỉ ca ngày CM2", time: "08:30 - 16:30" },
  { code: "WCM3", description: "Làm việc ngày nghỉ ca ngày CM3", time: "09:50 - 17:50" },
  { code: "ALLHA", description: "Làm việc ngày nghỉ ca hành chánh", time: "00:00 - 24:00" },
  { code: "HCAA1", description: "Làm việc ca ngày ngày lễ", time: "07:00 - 19:00" },
  { code: "HCAA2", description: "Làm việc ngày lễ A2", time: "06:00 - 14:00" },
  { code: "HCAA3", description: "Làm việc ca ngày ngày lễ", time: "07:00 - 15:00" },
  { code: "HCAB1", description: "Làm việc ca đêm ngày lễ", time: "19:00 - 07:00" },
  { code: "HCAB2", description: "Làm việc ngày lễ ca ngày 2", time: "14:00 - 22:00" },
  { code: "HCAB4", description: "Làm việc ca đêm ngày lễ 2", time: "22:00 - 06:00" },
  { code: "HCAB5", description: "Làm việc ca đêm ngày lễ", time: "21:00 - 05:00" },
  { code: "HHA", description: "Làm việc ngày lễ ca hành chánh", time: "00:00 - 24:00" },
]

