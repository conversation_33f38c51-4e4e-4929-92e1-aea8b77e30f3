<!-- eslint-disable no-unused-vars -->
<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { FileText, Search } from 'lucide-vue-next';

interface Props {
  searchValue: string;
  hasActiveFilters: boolean;
}

interface Emits {
  (e: 'clear-filters'): void;
  (e: 'request-leave'): void;
}

defineProps<Props>();
const emit = defineEmits<Emits>();

const clearFilters = () => {
  emit('clear-filters');
};

const requestLeave = () => {
  emit('request-leave');
};
</script>

<template>
  <div class="py-12 text-center">
    <div
      class="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-gray-100"
    >
      <component
        :is="searchValue.trim() ? Search : FileText"
        class="h-8 w-8 text-gray-400"
      />
    </div>
    <h3 class="mb-2 text-lg font-medium text-gray-900">
      {{
        searchValue.trim() ? 'No matching results' : 'No leave records found'
      }}
    </h3>
    <p class="mb-6 text-gray-500">
      {{
        searchValue.trim()
          ? `No leave records match "${searchValue}". Try different keywords.`
          : hasActiveFilters
            ? 'Try adjusting your filters'
            : "You haven't submitted any leave requests yet"
      }}
    </p>
    <div class="flex justify-center gap-3">
      <Button
        variant="outline"
        size="lg"
        v-if="searchValue.trim() || hasActiveFilters"
        @click="clearFilters"
      >
        Clear {{ searchValue.trim() ? 'Search' : 'Filters' }}
      </Button>
      <Button variant="blue" size="lg" @click="requestLeave">
        Request Leave
      </Button>
    </div>
  </div>
</template>
