<script setup lang="ts">
import logo from '@/assets/images/sharp.png';
import { Button } from '@/components/ui/button';
import { HOME } from '@/constants/routes';
import { useUserStore } from '@/stores/user';
import { isValidEmail } from '@/utils/validation';
import { IonPage } from '@ionic/vue';
import { Loader } from 'lucide-vue-next';
import type { FormInst, FormRules } from 'naive-ui';
import { NForm, NFormItem, NInput } from 'naive-ui';
import { computed, reactive, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';
import { toast } from 'vue-sonner';

const { t } = useI18n();
const router = useRouter();
const formRef = ref<FormInst | null>(null);
const userStore = useUserStore();

const formValue = reactive({
  username: '',
  password: '',
  rememberMe: false,
});

const rules: FormRules = {
  username: [
    {
      required: true,
      message: t('auth.login.please_enter_email'),
      trigger: ['blur', 'input'],
    },
    {
      validator: (_, value) => !value || isValidEmail(value),
      message: t('auth.login.valid_email'),
      trigger: ['blur', 'input'],
    },
  ],
  password: [
    {
      required: true,
      message: t('auth.login.please_enter_password'),
      trigger: ['blur', 'input'],
    },
    {
      min: 6,
      message: t('auth.login.valid_password'),
      trigger: ['blur', 'input'],
    },
  ],
};

const loading = computed(() => userStore.isLoading);

const handleLogin = () => {
  formRef.value?.validate(async (errors) => {
    if (errors) {
      return;
    }

    const result = await userStore.login(formValue.username, formValue.password);

    if (result.success) {
      toast.success(result.message || t('auth.login.login_success'));
      router.push(HOME);
    } else {
      toast.error(result.message || t('auth.login.login_failed'));
    }
  });
};
</script>

<template>
  <ion-page>
    <div class="flex h-full w-full items-center justify-center">
      <div class="w-full max-w-md px-4">
        <div class="mb-6 text-center">
          <RouterLink :to="HOME">
            <img :src="logo" alt="Logo" class="mx-auto h-16" />
          </RouterLink>
          <h1 class="mt-5 text-2xl font-semibold text-neutral-800">
            {{ t('auth.login.title') }}
          </h1>
        </div>
        <div class="login-container">
          <NForm ref="formRef" :model="formValue" :rules="rules" class="login-form">
            <NFormItem label-class="font-semibold" :label="$t('auth.login.username')" path="username">
              <NInput v-model:value="formValue.username" type="text" :placeholder="$t('auth.login.email_placeholder')"
                clearable size="large" />
            </NFormItem>

            <NFormItem label-class="font-semibold" :label="$t('auth.login.password')" path="password">
              <NInput v-model:value="formValue.password" type="password"
                :placeholder="$t('auth.login.password_placeholder')" show-password-on="click" size="large" />
            </NFormItem>

            <div class="login-options">
              <RouterLink to="#" class="forgot-password">{{
                t('auth.login.forgot_password')
              }}</RouterLink>
            </div>

            <Button size="lg" @click="handleLogin" class="w-full" :disabled="loading">
              <template v-if="loading">
                <Loader class="size-5 animate-spin" /> {{ t('auth.common.login') }}
              </template>
              <template v-else>{{ t('auth.login.login') }}</template>
            </Button>
          </NForm>

          <div class="signup-prompt">
            <span>{{ t('auth.login.dont_have_account') }}</span>
            <RouterLink to="#" class="signup-link">{{
              t('auth.login.sign_up')
            }}</RouterLink>
          </div>
        </div>
      </div>
    </div>
  </ion-page>
</template>

<style scoped>
.login-container {
  padding: 20px;
  max-width: 400px;
  margin: 0 auto;
}

.login-form {
  margin-bottom: 24px;
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.forgot-password {
  font-size: 14px;
  color: #1677ff;
  text-decoration: none;
}

.signup-prompt {
  text-align: center;
  font-size: 14px;
  margin-top: 32px;
}

.signup-link {
  color: #1677ff;
  margin-left: 4px;
  text-decoration: none;
}

:deep(.n-form-item .n-form-item-label) {
  padding-bottom: 8px;
}

:deep(.n-input) {
  border-radius: 8px;
}

:deep(.n-button) {
  border-radius: 8px;
}
</style>
