import { defineStore } from 'pinia';

export const useAuthRoleStore = defineStore('auth-role', {
  state: () => ({
    roleId: null as string | null,
    roleName: null as string | null,
  }),
  actions: {
    setRole(role: string, roleName: string) {
      this.roleId = role;
      this.roleName = roleName;
    },
    clearRole() {
      this.roleId = null;
      this.roleName = null;
    },
  },
  persist: true,
});
