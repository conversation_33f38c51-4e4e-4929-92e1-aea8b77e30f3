declare module '@ionic/vue' {
  import { Plugin } from 'vue';

  export const IonicVue: Plugin;
  export const IonApp: any;
  export const IonContent: any;
  export const IonPage: any;
  export const IonRouterOutlet: any;
  export const IonTabs: any;
  export const IonTabBar: any;
  export const IonTabButton: any;
  export const IonIcon: any;
  export const IonLabel: any;
  export const IonHeader: any;
  export const IonToolbar: any;
  export const IonTitle: any;
}

declare module '@ionic/vue-router' {
  import { RouteRecordRaw } from 'vue-router';

  export function createRouter(options: {
    history: any;
    routes: RouteRecordRaw[];
  }): any;

  export function createWebHistory(base?: string): any;
}
