import { getNotifications, removeNotification } from '@/services/notifications.service';
import { useMutation, useQuery, useQueryClient } from '@tanstack/vue-query';

import { computed } from 'vue';

export function useNotifications() {
  const queryClient = useQueryClient();

  const notificationQuery = useQuery({
    queryKey: ['notifications'],
    queryFn: getNotifications,
  });

  const removeNotificationMutation = useMutation({
    mutationFn: (id: number) => removeNotification(id),
    onSuccess: () => {
      // Invalidate and refetch notifications after successful removal
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
    },
  });

  const data = computed(() => notificationQuery.data.value?.data?.notifications || []);
  const isLoading = computed(() => notificationQuery.isLoading.value);
  const error = computed(() => notificationQuery.error.value);
  const refetch = notificationQuery.refetch;

  const handleRemoveNotification = async (id: number) => {
    try {
      await removeNotificationMutation.mutateAsync(id);
    } catch (error) {
      console.error('Failed to remove notification:', error);
      throw error;
    }
  };

  return {
    data,
    isLoading,
    error,
    refetch,
    notificationQuery,
    removeNotificationMutation,
    handleRemoveNotification,
  };
}
