<template>
  <div class="-mt-5 grid w-full grid-cols-2 gap-3 lg:grid-cols-4">
    <template v-if="!isLoading && stats">
      <div class="rounded-lg bg-white p-4 shadow-sm">
        <div class="text-sm font-medium text-gray-500">
          {{ $t('attendance.overtime.summary.total_requests') }}
        </div>
        <div class="mt-1 text-xl font-bold text-gray-900">
          {{ stats?.total_requests }}
        </div>
      </div>

      <div class="rounded-lg bg-white p-4 shadow-sm">
        <div class="text-sm font-medium text-gray-500">
          {{ $t('attendance.overtime.summary.approved_requests') }}
        </div>
        <div class="mt-1 text-xl font-bold text-emerald-600">
          {{ stats?.total_approves }}
        </div>
      </div>

      <div class="rounded-lg bg-white p-4 shadow-sm">
        <div class="text-sm font-medium text-gray-500">
          {{ $t('attendance.overtime.summary.pending_requests') }}
        </div>
        <div class="mt-1 text-xl font-bold text-amber-600">
          {{ stats?.total_pendings }}
        </div>
      </div>

      <div class="rounded-lg bg-white p-4 shadow-sm">
        <div class="text-sm font-medium text-gray-500">
          {{ $t('attendance.overtime.summary.total_mins') }}
        </div>
        <div class="mt-1 text-xl font-bold text-blue-600">
          {{ stats?.total_mins }}{{ $t('attendance.overtime.card.mins_suffix') }}
        </div>
      </div>
    </template>

    <template v-else>
      <div v-for="n in 4" :key="n" class="rounded-lg bg-white p-4 shadow-sm">
        <Skeleton class="h-4 w-24 mb-2" />
        <Skeleton class="h-6 w-12" />
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useAttendance } from '@/composables/useAttendance';
import { Skeleton } from '@/components/ui/skeleton'; // chỉnh path theo project

const { getOvertimeStatstics } = useAttendance();
const isLoading = computed(() => getOvertimeStatstics.isLoading.value);
const stats = computed(() => getOvertimeStatstics.data.value);
</script>
