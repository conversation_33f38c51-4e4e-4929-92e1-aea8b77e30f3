<script setup lang="ts">
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { useManagementRequests } from '@/composables/useManagementRequests';
import { Status } from '@/enums';
import type { ManagementRequest } from '@/mocks/management-requests';
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';

interface Props {
  requests: ManagementRequest[];
  statusFilter: Status | 'all';
}

const props = defineProps<Props>();

const emit = defineEmits<{
  'update:statusFilter': [value: Status | 'all'];
  approve: [id: number];
  reject: [id: number];
  reset: [id: number];
}>();

const { getStatusText, getStatusColor, getTypeText } = useManagementRequests();
const { t } = useI18n();

const filteredRequests = computed(() => {
  if (props.statusFilter === 'all') {
    return props.requests;
  }

  return props.requests.filter(
    (request) => request.status === props.statusFilter,
  );
});

const statusOptions = [
  { label: t('common.status.all'), value: 'all' as const },
  { label: t('common.status.pending'), value: Status.PENDING },
  { label: t('common.status.approved'), value: Status.APPROVED },
  { label: t('common.status.rejected'), value: Status.REJECTED },
];

// Convert status filter to string for tabs
const tabValue = computed({
  get: () => String(props.statusFilter),
  set: (value: string | number) => {
    const stringValue = String(value);
    const statusValue =
      stringValue === 'all' ? 'all' : (Number(stringValue) as Status);
    emit('update:statusFilter', statusValue);
  },
});

// Fix: Accept StringOrNumber type
const handleTabChange = (value: string | number) => {
  const stringValue = String(value);
  const statusValue =
    stringValue === 'all' ? 'all' : (Number(stringValue) as Status);
  emit('update:statusFilter', statusValue);
};
</script>

<template>
  <div class="requests-tab">
    <!-- Status Filter using Tabs -->
    <Tabs
      :model-value="tabValue"
      @update:model-value="handleTabChange"
      class="mb-6"
    >
      <TabsList class="grid w-full grid-cols-4">
        <TabsTrigger
          v-for="option in statusOptions"
          :key="option.value"
          :value="String(option.value)"
        >
          {{ option.label }}
        </TabsTrigger>
      </TabsList>
    </Tabs>

    <!-- Requests List -->
    <div class="space-y-4">
      <div
        v-for="request in filteredRequests"
        :key="request.id"
        class="rounded-lg border bg-white p-4 shadow-sm"
      >
        <div class="mb-3 flex items-start justify-between">
          <div>
            <h3 class="text-lg font-semibold">{{ request.employeeName }}</h3>
            <p class="text-sm text-gray-600">{{ request.date }}</p>
          </div>
          <div class="flex gap-2">
            <span
              :class="[
                'rounded px-2 py-1 text-xs font-medium',
                `bg-${getStatusColor(request.status)}-100 text-${getStatusColor(request.status)}-800`,
              ]"
            >
              {{ getStatusText(request.status) }}
            </span>
            <span
              class="rounded bg-gray-100 px-2 py-1 text-xs font-medium text-gray-800"
            >
              {{ getTypeText(request.type) }}
            </span>
          </div>
        </div>

        <p class="mb-4 text-gray-700">{{ request.details }}</p>

        <div
          v-if="request.status === Status.REJECTED && request.rejectionReason"
          class="mb-4 rounded border-l-4 border-red-400 bg-red-50 p-3"
        >
          <p class="text-sm text-red-700">
            <span class="font-medium"
              >{{ t('management.requests.rejection_reason') }}:</span
            >
            {{ request.rejectionReason }}
          </p>
        </div>

        <!-- Action Buttons -->
        <div class="flex justify-end gap-2">
          <button
            v-if="request.status === Status.PENDING"
            @click="emit('approve', request.id)"
            class="rounded bg-green-500 px-4 py-2 text-sm text-white transition-colors hover:bg-green-600"
          >
            {{ t('management.requests.actions.approve') }}
          </button>

          <button
            v-if="request.status === Status.PENDING"
            @click="emit('reject', request.id)"
            class="rounded bg-red-500 px-4 py-2 text-sm text-white transition-colors hover:bg-red-600"
          >
            {{ t('management.requests.actions.reject') }}
          </button>

          <button
            v-if="request.status !== Status.PENDING"
            @click="emit('reset', request.id)"
            class="hover:blue-600 rounded bg-blue-500 px-4 py-2 text-sm text-white transition-colors"
          >
            {{ t('management.requests.actions.reset') }}
          </button>
        </div>
      </div>

      <!-- Empty State -->
      <div
        v-if="filteredRequests.length === 0"
        class="py-8 text-center text-gray-500"
      >
        <p>{{ t('management.requests.no_requests') }}</p>
      </div>
    </div>
  </div>
</template>

<style scoped>
.requests-tab {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 16px;
}
</style>
