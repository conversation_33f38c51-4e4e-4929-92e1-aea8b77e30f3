import { normalizePath } from '@/utils/normalize-path';
import { defineStore } from 'pinia';

export const useHistoryStore = defineStore('history', {
  state: () => ({
    rawHistory: [] as string[],
  }),

  getters: {
    cleanedHistory: (state) => {
      const seen = new Set<string>();
      const result: string[] = [];

      for (let i = state.rawHistory.length - 1; i >= 0; i--) {
        const normalized = normalizePath(state.rawHistory[i]);

        if (!seen.has(normalized)) {
          seen.add(normalized);
          result.unshift(normalized);
        }
      }

      return result;
    },
  },

  actions: {
    addPath(path: string) {
      this.rawHistory.push(path);

      const cleaned = this.cleanedHistory;

      localStorage.setItem('history_urls', JSON.stringify(cleaned));
    },

    loadFromStorage() {
      try {
        const stored = localStorage.getItem('history_urls');

        if (stored) {
          this.rawHistory = JSON.parse(stored);
        } else {
          this.rawHistory = [];
          localStorage.setItem('history_urls', JSON.stringify([]));
        }
      } catch {
        this.rawHistory = [];
        localStorage.setItem('history_urls', JSON.stringify([]));
      }
    },

    clearHistory() {
      this.rawHistory = [];
      localStorage.removeItem('history_urls');
    },

    getPreviousUrl(currentPath: string): string | null {
      const normalizedCurrent = normalizePath(currentPath);
      const cleaned = this.cleanedHistory;
      const currentIndex = cleaned.lastIndexOf(normalizedCurrent);

      if (currentIndex <= 0) return null;

      return cleaned[currentIndex - 1];
    },
  },
});
