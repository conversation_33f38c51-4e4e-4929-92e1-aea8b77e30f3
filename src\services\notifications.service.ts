import type { NotificationResponse } from '@/interfaces/common';
import apiClient from '@/api/axios-instance';

export const getNotifications = async (): Promise<NotificationResponse> => {
  const { data } = await apiClient.get<NotificationResponse>('/notifications');
  return data;
};

export const removeNotification = async (id: number) => {
  await apiClient.delete(`/notifications/${id}/clear`);
};
