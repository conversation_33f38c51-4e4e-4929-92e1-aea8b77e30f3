<script setup lang="ts">
import AdvanceBalance from '@/components/expense/AdvanceBalance.vue'
import ExpenseSummary from '@/components/expense/ExpenseSummary.vue'
import RecentExpenses from '@/components/expense/RecentExpenses.vue'
import { IonPage } from '@ionic/vue'
</script>

<template>
  <ion-page class="ion-padding">
    <div class="flex flex-col items-center gap-y-7 h-full scroll-container">
      <ExpenseSummary />
      <RecentExpenses />
      <AdvanceBalance />
    </div>
  </ion-page>
</template>
