import {
  NDialogProvider,
  NLoadingBarProvider,
  NMessageProvider,
  NNotificationProvider,
  NSelect,
  create,
} from 'naive-ui'

import type { GlobalThemeOverrides } from 'naive-ui'

interface NaiveUiOptions {
  themeOverrides?: GlobalThemeOverrides
}

export default (options: NaiveUiOptions = {}) => {
  return create({
    components: [
      NMessageProvider,
      NDialogProvider,
      NNotificationProvider,
      NLoadingBarProvider,
      NSelect,
    ],
    ...options,
  })
}
