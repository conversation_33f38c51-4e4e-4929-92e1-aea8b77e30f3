/**
 * Check if a string is a valid email
 * @param email Email to validate
 * @returns Whether the email is valid
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * Check if a string is a valid URL
 * @param url URL to validate
 * @returns Whether the URL is valid
 */
export function isValidUrl(url: string): boolean {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

/**
 * Check if a password meets minimum requirements
 * @param password Password to validate
 * @param minLength Minimum length
 * @returns Whether the password is valid
 */
export function isValidPassword(password: string, minLength = 8): boolean {
  if (password.length < minLength) return false
  
  // At least one uppercase letter, one lowercase letter, and one number
  const hasUppercase = /[A-Z]/.test(password)
  const hasLowercase = /[a-z]/.test(password)
  const hasNumber = /[0-9]/.test(password)
  
  return hasUppercase && hasLowercase && hasNumber
}
