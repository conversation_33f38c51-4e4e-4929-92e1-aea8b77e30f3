<script setup lang="ts">
import InstallPrompt from '@/components/ui/pwa/InstallPrompt.vue';
import ActionLayout from '@/layouts/ActionLayout.vue';
import AuthLayout from '@/layouts/AuthLayout.vue';
import RootLayout from '@/layouts/RootLayout.vue';
import { useHistoryStore } from '@/stores/history';
import { IonApp } from '@ionic/vue';
import { VueQueryDevtools } from '@tanstack/vue-query-devtools';
import { computed, onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';
import { Toaster } from 'vue-sonner';
import 'vue-sonner/style.css';

const route = useRoute();
const showInstallPrompt = ref(!localStorage.getItem('pwa-prompt-dismissed'));
const isShowDevtools = import.meta.env.VITE_SHOW_DEVTOOLS === 'true';

onMounted(() => {
  const history = useHistoryStore();
  history.loadFromStorage();
});

const layoutComponent = computed(() => {
  switch (route.meta.layout) {
    case 'auth':
      return AuthLayout;
    case 'action':
      return ActionLayout;
    default:
      return RootLayout;
  }
});
</script>

<template>
  <Toaster richColors :duration="3000" position="top-right" />
  <ion-app>
    <component :is="layoutComponent">
      <router-view />
    </component>
    <VueQueryDevtools v-if="isShowDevtools" :initial-is-open="isShowDevtools" />
    <InstallPrompt v-model="showInstallPrompt" />
  </ion-app>
</template>
