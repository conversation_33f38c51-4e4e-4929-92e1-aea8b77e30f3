<script setup lang="ts">
import { useAttendance } from '@/composables/useAttendance';
import { useLocale } from '@/composables/useLocale';
import { LIMIT } from '@/constants';
import { getStatusClass, getStatusText } from '@/constants/leave-history';
import * as ROUTES from '@/constants/routes';
import { formatDate } from '@/utils/format';
import { getLocalizedPath } from '@/utils/localized-navigation';
import { computed, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import OvertimeDetailModal from './OvertimeDetailModal.vue';
import type { Overtime } from '@/interfaces/attendance';
import { Calendar, Clock } from 'lucide-vue-next';

const { t } = useI18n();
const { locale } = useLocale();
const { latestOvertimeQuery } = useAttendance();

const { data: latestOvertimeData, isLoading } = latestOvertimeQuery(LIMIT);

const latestOvertime = computed(() => latestOvertimeData.value || []);
const hasRequests = computed(() => latestOvertime.value.length > 0);
const newPath = getLocalizedPath(ROUTES.OVERTIME_HISTORY, locale.value);

// Modal state
const showModal = ref(false);
const selectedRequest = ref<Overtime | null>(null);

// Handle card click to show modal
const handleCardClick = (request: Overtime) => {
  selectedRequest.value = request;
  showModal.value = true;
};

// Close modal
const closeModal = () => {
  showModal.value = false;
  selectedRequest.value = null;
};
</script>

<template>
  <div class="mb-7 flex w-full flex-col gap-4">
    <div class="text-lg font-semibold text-gray-800 capitalize">
      {{ t('attendance.recent.title') }}
    </div>

    <!-- Loading Skeleton -->
    <div v-if="isLoading" class="mb-4 flex flex-col gap-2">
      <div v-for="i in LIMIT" :key="`skeleton-${i}`"
        class="flex animate-pulse flex-col gap-1 rounded border border-gray-200 p-3">
        <div class="flex justify-between">
          <div class="h-4 w-24 rounded bg-gray-200"></div>
          <div class="h-5 w-16 rounded-full bg-gray-200"></div>
        </div>
        <div class="mt-1 h-3 w-20 rounded bg-gray-200"></div>
        <div class="h-3 w-full rounded bg-gray-200"></div>
        <div class="h-3 w-3/4 rounded bg-gray-200"></div>
      </div>
      <div class="mx-auto mt-2 h-4 w-20 rounded bg-gray-200"></div>
    </div>

    <!-- No Requests State -->
    <div v-else-if="!hasRequests" class="flex items-center justify-center p-5">
      <p class="w-full text-center text-sm text-gray-500">
        {{ t('attendance.recent.no_requests') }}
      </p>
    </div>

    <!-- Loaded Requests -->
    <div v-else class="mb-4 flex flex-col gap-2">
      <div v-for="item in latestOvertime" :key="item.id" @click="handleCardClick(item)"
        class="flex flex-col gap-3 rounded border border-gray-200 p-3 cursor-pointer hover:bg-gray-50 hover:border-gray-300 transition-colors">
        <div class="flex justify-between">
          <span class="text-sm font-medium">
            {{
              t('attendance.overtime.card.overtime_id', {
                id: item.serious_number,
              })
            }}
          </span>
          <span :class="`rounded-full px-2 py-1 text-xs ${getStatusClass(item.status)}`">
            {{ getStatusText(item.status) }}
          </span>
        </div>
        <div class="flex items-center gap-1">
          <Calendar class="text-gray-600 w-4 h-4" />
          <div class="text-xs text-gray-500">
            {{ formatDate(item.additional_day, locale) }}
          </div>
        </div>
        <div class="flex items-center gap-1">
          <Clock class="text-gray-600 w-4 h-4" />
          <div class="text-xs text-gray-700">{{ item.reason }}</div>
        </div>
      </div>
    </div>
    <!-- View All Link -->
    <router-link :to="newPath" class="block text-center text-sm font-medium capitalize underline underline-offset-2">
      {{ t('attendance.recent.view_all') }}
    </router-link>

    <!-- Overtime Detail Modal -->
    <OvertimeDetailModal v-if="selectedRequest" v-model:show="showModal" :overtime="selectedRequest"
      @close="closeModal" />
  </div>
</template>
