import type { ApproveOvertimeRequestPayload, ApproveOvertimeRequestResponse, DetailedAttendanceApiResponse, ManagementOvertimeResponse, Overtime, OvertimeItemResponse, OvertimeRequest, OvertimeRequestResponse, OvertimeResponse, OvertimeStats, OvertimeStatsResponse, PaginatedAttendanceResponse, PaginatedOvertimeResponse, UpdateEmployeeStatusPayload, WorkShift, WorkShiftResponse } from '@/interfaces/attendance';

import { PAGE_SIZE } from '@/constants';
import apiClient from '@/api/axios-instance';

export const registerOvertime = async (
  payload: OvertimeRequest,
): Promise<OvertimeRequestResponse> => {
  const { data } = await apiClient.post<OvertimeRequestResponse>(
    '/attendance/register-overtime',
    payload,
  );
  return data;
};

export const getOvertimeRequest = async (
  id: number,
): Promise<OvertimeItemResponse> => {
  const { data } = await apiClient.get<OvertimeItemResponse>(
    `/attendance/overtime-requests/${id}`,
  );

  return data;
};

export const getGroupOvertimeRequest = async (
  group_code:string,):
  Promise<OvertimeItemResponse> => {
  const { data } = await apiClient.get<OvertimeItemResponse>(
    `/attendance/overtime-requests/group/${group_code}`,
  );

  return data;
};

export const updateWorkShift = async (
  id: number,
  payload: OvertimeRequest,
): Promise<OvertimeRequestResponse> => {
  const { data } = await apiClient.put<OvertimeRequestResponse>(
    `/attendance/update-overtime/${id}`,
    payload,
  );

  return data;
};

export const updateWorkShiftGroup = async (
  group_code: string,
  payload: OvertimeRequest,
): Promise<OvertimeRequestResponse> => {
  const { data } = await apiClient.put<OvertimeRequestResponse>(
    `/attendance/group-overtime/update/${group_code}`,
    payload,
  );

  return data;
};

export const updateStatusOvertime = async (
  payload: UpdateEmployeeStatusPayload,
): Promise<OvertimeRequestResponse> => {
  const { data } = await apiClient.put<OvertimeRequestResponse>(
    `/attendance/employee-status`,
    payload
  );
  return data;
};

export const getWorkShifts = async (): Promise<WorkShift[]> => {
  const { data } = await apiClient.get<WorkShiftResponse>(
    '/attendance/shift-types',
  );

  return data.data ?? [];
};

export const getOvertimeStats = async (): Promise<OvertimeStats> => {
  const { data } = await apiClient.get<OvertimeStatsResponse>(
    '/attendance/overtime-stats',
  );

  return data.data;
};



export const getOvertimeRequests = async (
  filters?: Record<string, any>,
): Promise<OvertimeResponse> => {
  let url = '/attendance/overtime-requests';

  if (filters && Object.keys(filters).length > 0) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== null && value !== undefined && value !== '') {
        params.append(key, String(value));
      }
    });
    url += `?${params.toString()}`;
  }

  const { data } = await apiClient.get<OvertimeResponse>(url);

  return data;
};

export const getLatestOvertimeRequest = async (
  limit: number = PAGE_SIZE,
): Promise<Overtime[]> => {
  const { data } = await apiClient.get<OvertimeResponse>(
    '/attendance/overtime-requests/latest',
    {
      params: {
        limit,
      },
    },
  );

  return data.data ?? [];
};

export const getAllOvertimeRequests = async (
  filters?: Record<string, any>,
): Promise<PaginatedOvertimeResponse> => {
  let url = '/attendance/overtime-requests/all';

  if (filters && Object.keys(filters).length > 0) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== null && value !== undefined && value !== '') {
        params.append(key, String(value));
      }
    });
    url += `?${params.toString()}`;
  }

  const { data } = await apiClient.get<PaginatedOvertimeResponse>(url);

  return data;
};

export const getManagementOvertimeRequests = async (
  filters?: Record<string, any>,
): Promise<ManagementOvertimeResponse> => {
  let url = '/attendance/overtime-requests/management';

  if (filters && Object.keys(filters).length > 0) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== null && value !== undefined && value !== '') {
        params.append(key, String(value));
      }
    });
    url += `?${params.toString()}`;
  }

  const { data } = await apiClient.get<ManagementOvertimeResponse>(url);

  return data;
};

export const getAttendanceLogs = async (
  filters?: Record<string, any>,
): Promise<PaginatedAttendanceResponse> => {
  let url = '/attendance/logs';

  if (filters && Object.keys(filters).length > 0) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== null && value !== undefined && value !== '') {
        params.append(key, String(value));
      }
    });
    url += `?${params.toString()}`;
  }

  const { data } = await apiClient.get<PaginatedAttendanceResponse>(url);

  return data;
};

export const getDetailsAttandanceLogs = async (
  filters?: Record<string, any>,
): Promise<DetailedAttendanceApiResponse> => {
  let url = '/attendance/timesheet-table';

  if (filters && Object.keys(filters).length > 0) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== null && value !== undefined && value !== '') {
        params.append(key, String(value));
      }
    });
    url += `?${params.toString()}';
    `;
  }

  const { data } = await apiClient.get<DetailedAttendanceApiResponse>(url);

  return data;
};


export const approveOvertimeRequest = async (
  payload: ApproveOvertimeRequestPayload,
): Promise<ApproveOvertimeRequestResponse> => {
  const { data } = await apiClient.put<ApproveOvertimeRequestResponse>(
    '/attendance/overtime-requests/approve',
    payload,
  );

  return data;
};

