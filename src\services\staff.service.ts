import type { StaffResponse } from '@/interfaces/staff';
import apiClient from '@/api/axios-instance';

export const getStaffs = async (): Promise<StaffResponse> => {
  const { data } = await apiClient.get<StaffResponse>('/staffs');

  return data;
};
export const getStaffApprovers = async (): Promise<StaffResponse> => {
  const { data } = await apiClient.get<StaffResponse>('/staff/approvers');

  return data;
};
