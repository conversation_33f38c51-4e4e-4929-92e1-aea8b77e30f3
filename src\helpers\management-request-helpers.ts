import { RequestType, Status } from '@/enums';

import { mockManagementRequests } from '@/mocks/management-requests';

export const filterRequestsByType = (type: RequestType) => {
  return mockManagementRequests.value.filter(
    (request) => request.type === type,
  );
};

export const filterRequestsByStatus = (status: Status) => {
  return mockManagementRequests.value.filter(
    (request) => request.status === status,
  );
};

export const fetchLeaveRequests = () => {
  return filterRequestsByType(RequestType.REQUISITION);
};

export const fetchAttendanceRequests = () => {
  return mockManagementRequests.value.filter(
    (request) => request.type === RequestType.ADDITIONAL,
  );
};
