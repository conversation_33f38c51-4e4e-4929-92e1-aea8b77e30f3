<!-- eslint-disable no-unused-vars -->
<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import debounce from 'lodash.debounce';
import { Filter, Search, X } from 'lucide-vue-next';

interface Props {
  modelValue: string;
  showFilter: boolean;
}

interface Emits {
  (e: 'update:modelValue', value: string): void;
  (e: 'toggle-filter'): void;
}

defineProps<Props>();
const emit = defineEmits<Emits>();

const debouncedSearch = debounce((value: string) => {
  emit('update:modelValue', value);
}, 300);

const updateSearch = (value: string | number) => {
  debouncedSearch(value.toString());
};

const clearSearch = () => {
  emit('update:modelValue', '');
};

const toggleFilter = () => {
  emit('toggle-filter');
};
</script>

<template>
  <div class="w-full bg-white px-4 py-3 shadow">
    <div class="flex items-center gap-4">
      <div class="relative w-full max-w-sm items-center">
        <Input
          id="search"
          type="text"
          placeholder="Search leave records..."
          class="px-8"
          :model-value="modelValue"
          @update:model-value="updateSearch"
        />
        <span
          class="absolute inset-y-0 start-0 flex items-center justify-center px-2"
        >
          <Search class="text-muted-foreground size-4" />
        </span>
        <button
          v-if="modelValue"
          @click="clearSearch"
          class="text-muted-foreground hover:text-foreground absolute inset-y-0 end-0 flex cursor-pointer items-center justify-center px-2 transition-colors"
        >
          <X class="size-4" />
        </button>
      </div>

      <Button variant="outline" size="icon" @click="toggleFilter">
        <Filter class="text-gray-500" />
      </Button>
    </div>
  </div>
</template>
