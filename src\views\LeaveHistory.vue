<script setup lang="ts">
import LeaveCard from '@/components/leaves/LeaveCard.vue';
import LeaveCardSkeleton from '@/components/leaves/LeaveCardSkeleton.vue';
import LeaveEmptyState from '@/components/leaves/LeaveEmptyState.vue';
import LeaveFilter from '@/components/leaves/LeaveFilter.vue';
import LeaveFilterSkeleton from '@/components/leaves/LeaveFilterSkeleton.vue';
import LeaveSearchBar from '@/components/leaves/LeaveSearchBar.vue';
import LeaveSearchBarSkeleton from '@/components/leaves/LeaveSearchBarSkeleton.vue';
import LeaveStatistics from '@/components/leaves/LeaveStatistics.vue';
import LeaveStatisticsSkeleton from '@/components/leaves/LeaveStatisticsSkeleton.vue';
import { useLeaveRequests } from '@/composables/useLeaveRequests';
import { useLocale } from '@/composables/useLocale';
import { NEW_LEAVE_APPLICATIONS } from '@/constants/routes';
import { Status } from '@/enums';
import type { Leave } from '@/interfaces/leave';
import { getLocalizedPath } from '@/utils/localized-navigation';
import { IonPage } from '@ionic/vue';
import { computed, ref, watchEffect } from 'vue';
import { useRouter } from 'vue-router';

interface FilterData {
  type: string;
  status: string;
  fromDate: string;
  toDate: string;
}

const leaves = ref<Leave[]>([]);

const router = useRouter();
const { locale } = useLocale();
const { listQuery } = useLeaveRequests();

const isLoading = computed(() => listQuery.isLoading.value);

watchEffect(() => {
  if (listQuery.data?.value) {
    leaves.value = [...(listQuery.data.value.data ?? [])];
  }
});

const filters = ref<FilterData>({
  type: '',
  status: '',
  fromDate: '',
  toDate: '',
});

const searchValue = ref<string>('');
const showFilter = ref(false);

const hasActiveFilters = computed(() => {
  return !!(
    filters.value.type ||
    filters.value.status ||
    filters.value.fromDate ||
    filters.value.toDate ||
    searchValue.value.trim()
  );
});

const filteredLeaves = computed(() => {
  let filtered = leaves.value;

  if (searchValue.value.trim()) {
    const query = searchValue.value.toLowerCase().trim();
    filtered = filtered.filter(
      (leave) =>
        leave.subject.toLowerCase().includes(query) ||
        leave.reason.toLowerCase().includes(query),
    );
  }

  if (filters.value.type !== '' && filters.value.type !== 'all') {
    filtered = filtered.filter(
      (leave) => leave.type_of_leave === Number(filters.value.type),
    );
  }

  if (filters.value.status !== '' && filters.value.status !== 'all') {
    filtered = filtered.filter(
      (leave) => leave.status === Number(filters.value.status),
    );
  }

  if (filters.value.status !== '' && filters.value.status !== 'all') {
    filtered = filtered.filter(
      (leave) => leave.status === Number(filters.value.status),
    );
  }

  if (filters.value.fromDate) {
    filtered = filtered.filter(
      (leave) => leave.start_time >= filters.value.fromDate,
    );
  }

  if (filters.value.toDate) {
    filtered = filtered.filter(
      (leave) => leave.end_time <= filters.value.toDate,
    );
  }

  return filtered.sort(
    (a, b) =>
      new Date(b.start_time).getTime() - new Date(a.start_time).getTime(),
  );
});

const stats = computed(() => {
  const total = filteredLeaves.value.length;
  const approved = filteredLeaves.value.filter(
    (leave) => leave.status === Status.APPROVED,
  ).length;
  const pending = filteredLeaves.value.filter(
    (leave) => leave.status === Status.PENDING,
  ).length;
  const rejected = filteredLeaves.value.filter(
    (leave) => leave.status === Status.REJECTED,
  ).length;

  return { total, approved, pending, rejected };
});

// Methods
const toggleFilter = () => {
  showFilter.value = !showFilter.value;
};

const clearFilters = () => {
  filters.value = {
    type: '',
    status: '',
    fromDate: '',
    toDate: '',
  };
  searchValue.value = '';
};

const applyFilters = () => {
  showFilter.value = false;
};

const updateFilters = (newFilters: FilterData) => {
  filters.value = newFilters;
};

const requestLeave = () => {
  const newPath = getLocalizedPath(NEW_LEAVE_APPLICATIONS, locale.value);
  router.push(newPath);
};
</script>

<template>
  <ion-page>
    <div class="scroll-container flex h-full flex-col items-center gap-y-5">
      <template v-if="isLoading">
        <LeaveSearchBarSkeleton />
      </template>
      <template v-else>
        <LeaveSearchBar v-model="searchValue" :show-filter="showFilter" @toggle-filter="toggleFilter" />
      </template>

      <div class="space-y-5 px-4">
        <template v-if="isLoading && showFilter">
          <LeaveFilterSkeleton />
        </template>
        <template v-else-if="showFilter">
          <LeaveFilter :show="showFilter" :filters="filters" @update:show="showFilter = $event"
            @update:filters="updateFilters" @clear-filters="clearFilters" @apply-filters="applyFilters" />
        </template>

        <template v-if="isLoading">
          <LeaveStatisticsSkeleton />
        </template>
        <template v-else>
          <LeaveStatistics :stats="stats" />
        </template>
      </div>

      <!-- Leave History List -->
      <div class="mb-7 w-full px-4">
        <div v-if="isLoading" class="space-y-3">
          <LeaveCardSkeleton v-for="i in 3" :key="i" />
        </div>

        <div v-else class="space-y-3">
          <LeaveCard v-for="leave in filteredLeaves" :key="leave.id" :item="leave" />
        </div>

        <LeaveEmptyState v-if="!isLoading && filteredLeaves.length === 0" :search-value="searchValue"
          :has-active-filters="hasActiveFilters" @clear-filters="clearFilters" @request-leave="requestLeave" />
      </div>
    </div>
  </ion-page>
</template>
