import { computed, ref } from 'vue';

import type { Status } from '@/enums';

export function useOvertimeFilter() {
  // Filter state
  const filterStatus = ref<Status | 'all'>('all');
  const filterDateFrom = ref<string | null>(null);
  const filterDateTo = ref<string | null>(null);

  // Pagination state
  const currentPage = ref<number>(1);
  const itemsPerPage = ref<number>(20);

  // Check if any filters are active
  const hasActiveFilters = computed(() => {
    return Boolean(
      filterStatus.value !== 'all' ||
        filterDateFrom.value ||
        filterDateTo.value,
    );
  });

  // Convert filters to API parameters
  const filterParams = computed(() => {
    const params: Record<string, any> = {
      page: currentPage.value,
      limit: itemsPerPage.value,
    };

    if (filterStatus.value !== 'all') {
      params.status = filterStatus.value;
    }

    if (filterDateFrom.value) {
      params.date_from = filterDateFrom.value;
    }

    if (filterDateTo.value) {
      params.date_to = filterDateTo.value;
    }

    return params;
  });

  // Reset filters (but keep pagination)
  const resetFilters = () => {
    filterStatus.value = 'all';
    filterDateFrom.value = null;
    filterDateTo.value = null;
    currentPage.value = 1; // Reset to first page when clearing filters
  };

  // Pagination methods
  const goToPage = (page: number) => {
    currentPage.value = page;
  };

  const nextPage = () => {
    currentPage.value += 1;
  };

  const prevPage = () => {
    if (currentPage.value > 1) {
      currentPage.value -= 1;
    }
  };

  return {
    // Filter state
    filterStatus,
    filterDateFrom,
    filterDateTo,
    hasActiveFilters,
    filterParams,
    resetFilters,

    // Pagination state
    currentPage,
    itemsPerPage,
    goToPage,
    nextPage,
    prevPage,
  };
}
