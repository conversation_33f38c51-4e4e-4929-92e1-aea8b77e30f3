<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { useLocale } from '@/composables/useLocale';
import { formatDateLocalized } from '@/utils/format';
import {
  CalendarDays,
  Check,
  Clock,
  Inbox,
  RotateCcw,
  X,
} from 'lucide-vue-next';
import { NTag } from 'naive-ui';
import { useI18n } from 'vue-i18n';

interface Request {
  id: number;
  employeeName: string;
  type: string;
  details: string;
  date: string;
  status: string;
  staff_id: number;
  rejectionReason?: string;
}

interface Props {
  request: Request;
}

interface Emits {
  (e: 'approve', id: number): void;
  (e: 'reject', id: number): void;
  (e: 'reset', id: number): void;
}

defineProps<Props>();
defineEmits<Emits>();

const { t } = useI18n();
const { locale } = useLocale();

const getRequestTypeText = (type: string) => {
  switch (type) {
    case 'leave':
      return t('management.requests.types.leave');
    case 'overtime':
      return t('management.requests.types.overtime');
    case 'shift':
      return t('management.requests.types.shift');
    default:
      return t('management.requests.types.default');
  }
};

const getRequestIcon = (type: string) => {
  switch (type) {
    case 'leave':
      return CalendarDays;
    case 'overtime':
      return Clock;
    case 'shift':
      return Clock;
    default:
      return Inbox;
  }
};

const getStatusText = (status: string) => {
  switch (status) {
    case 'pending':
      return t('management.requests.status.pending');
    case 'approved':
      return t('management.requests.status.approved');
    case 'rejected':
      return t('management.requests.status.rejected');
    default:
      return '';
  }
};

const getStatusType = (status: string) => {
  switch (status) {
    case 'pending':
      return 'warning';
    case 'approved':
      return 'success';
    case 'rejected':
      return 'error';
    default:
      return 'default';
  }
};
</script>

<template>
  <Card class="p-0 transition-shadow hover:shadow-md">
    <CardContent class="py-4">
      <div class="flex flex-col gap-4 sm:items-center sm:justify-between">
        <div class="flex-grow">
          <div class="mb-2 flex items-center">
            <component
              :is="getRequestIcon(request.type)"
              class="mr-2 h-5 w-5 text-gray-600"
            />
            <span class="text-lg font-semibold">
              {{ request.employeeName }}
            </span>
            <n-tag
              :type="getStatusType(request.status)"
              class="ml-3"
              size="small"
              round
            >
              {{ getStatusText(request.status) }}
            </n-tag>
          </div>
          <p class="mb-1 text-sm text-gray-700">
            <span class="font-medium">
              {{ getRequestTypeText(request.type) }}:
            </span>
            {{ request.details }}
          </p>
          <p class="text-xs text-gray-500">
            {{ t('management.requests.submitted_date') }}:
            {{ formatDateLocalized(request.date, locale) }}
          </p>
          <p
            v-if="request.status === 'rejected' && request.rejectionReason"
            class="mt-1 text-xs text-red-600"
          >
            <span class="font-medium">
              {{ t('management.requests.rejection_reason') }}:
            </span>
            {{ request.rejectionReason }}
          </p>
        </div>

        <div class="flex w-full shrink-0 space-x-2">
          <template v-if="request.status === 'pending'">
            <Button
              type="button"
              size="sm"
              class="flex-1 bg-green-600 hover:bg-green-700"
              @click="$emit('approve', request.id)"
            >
              <Check class="mr-1 h-4 w-4" />
              {{ t('management.requests.actions.approve') }}
            </Button>
            <Button
              variant="destructive"
              size="sm"
              class="flex-1"
              @click="$emit('reject', request.id)"
            >
              <X class="mr-1 h-4 w-4" />
              {{ t('management.requests.actions.reject') }}
            </Button>
          </template>
          <template v-else>
            <Button
              variant="outline"
              size="sm"
              class="flex-1"
              @click="$emit('reset', request.id)"
            >
              <RotateCcw class="mr-1 h-4 w-4" />
              {{ t('management.requests.actions.reset') }}
            </Button>
          </template>
        </div>
      </div>
    </CardContent>
  </Card>
</template>
