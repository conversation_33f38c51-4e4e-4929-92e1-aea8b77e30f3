services:
  sharp-app:
    build:
      context: .
      dockerfile: Dockerfile
      # target: production
    container_name: sharp-vue-app
    restart: unless-stopped
    ports:
      - '8085:80'
      - '8443:443' # For future HTTPS support
    volumes:
      # Logs volume
      - nginx-logs:/var/log/nginx
    environment:
      - TZ=Asia/Ho_Chi_Minh
      # Firebase Configuration
      - VITE_FIREBASE_API_KEY=AIzaSyCacJADCxhOAbsKXoFNavk8vjBtLmsm-fc
      - VITE_FIREBASE_AUTH_DOMAIN=sharp-pwa.firebaseapp.com
      - VITE_FIREBASE_PROJECT_ID=sharp-pwa
      - VITE_FIREBASE_STORAGE_BUCKET=sharp-pwa.firebasestorage.app
      - VITE_FIREBASE_MESSAGING_SENDER_ID=747568647844
      - VITE_FIREBASE_APP_ID=1:747568647844:web:0a2e1da87dc1a78998a031
      - VITE_FIREBASE_MEASUREMENT_ID=G-DKZ9W6XVRW
      - VITE_FIREBASE_VAPID_KEY=BCRe8DnpEUxa4h__wmuMT-wpjfRT0gkWn8B6K57iTaqOZvV_EFvYluwnHJhkSK0cwSy-DmAqWomKWlk9ZQLWBs8
      # API Configuration
      - VITE_API_BASE_URL=localhost:8080/flutex_admin_api
      # Pusher Configuration
      - VITE_PUSHER_APP_KEY=a30e473765cbdf462ffb
      - VITE_PUSHER_CLUSTER=ap1
      # Development
      - VITE_SHOW_DEVTOOLS=false
    networks:
      - sharp-network
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost/health']
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Add a reverse proxy for multiple services
  # nginx-proxy:
  #   image: nginx:alpine
  #   container_name: sharp-proxy
  #   restart: unless-stopped
  #   ports:
  #     - "80:80"
  #     - "443:443"
  #   volumes:
  #     - ./proxy/nginx.conf:/etc/nginx/nginx.conf:ro
  #     - ./ssl:/etc/nginx/ssl:ro
  #   depends_on:
  #     - sharp-app
  #   networks:
  #     - sharp-network

volumes:
  nginx-logs:
    driver: local

networks:
  sharp-network:
    driver: bridge
