import { RequestType, Status } from '@/enums';

import { ref } from 'vue';

export interface ManagementRequest {
  id: number;
  employeeName: string;
  type: RequestType;
  details: string;
  date: string;
  status: Status;
  staff_id: number;
  rejectionReason?: string;
}

export const mockManagementRequests = ref<ManagementRequest[]>([
  {
    id: 1,
    employeeName: 'Nguyễn <PERSON>ăn <PERSON>',
    type: RequestType.REQUISITION,
    details: 'Nghỉ ốm từ 01/07/2025 đến 03/07/2025',
    date: '2025-06-25',
    status: Status.PENDING,
    staff_id: 1,
  },
  {
    id: 2,
    employeeName: 'Trần Thị B',
    type: RequestType.ADDITIONAL,
    details: 'Đăng ký tăng ca ngày 02/07/2025 (2 tiếng)',
    date: '2025-06-24',
    status: Status.PENDING,
    staff_id: 2,
  },
  {
    id: 3,
    employeeName: '<PERSON><PERSON>ăn <PERSON>',
    type: RequestType.ADDITIONAL,
    details: '<PERSON><PERSON>ng ký đổi ca làm việ<PERSON> từ ca sáng sang ca chiều ngày 05/07/2025',
    date: '2025-06-23',
    status: Status.PENDING,
    staff_id: 3,
  },
  {
    id: 4,
    employeeName: 'Phạm Thị D',
    type: RequestType.REQUISITION,
    details: 'Nghỉ phép năm từ 10/07/2025 đến 15/07/2025',
    date: '2025-06-22',
    status: Status.APPROVED,
    staff_id: 4,
  },
  {
    id: 5,
    employeeName: 'Hoàng Văn E',
    type: RequestType.ADDITIONAL,
    details: 'Đăng ký tăng ca ngày 01/07/2025 (3 tiếng)',
    date: '2025-06-21',
    status: Status.REJECTED,
    rejectionReason: 'Không đủ ngân sách cho tăng ca trong tháng này',
    staff_id: 5,
  },
  {
    id: 6,
    employeeName: 'Võ Thị F',
    type: RequestType.REQUISITION,
    details: 'Nghỉ thai sản từ 15/07/2025 đến 15/10/2025',
    date: '2025-06-20',
    status: Status.PENDING,
    staff_id: 6,
  },
  {
    id: 7,
    employeeName: 'Đặng Văn G',
    type: RequestType.ADDITIONAL,
    details: 'Đăng ký ca đêm thay vì ca ngày cho tuần tới',
    date: '2025-06-19',
    status: Status.APPROVED,
    staff_id: 7,
  },
]);
