import { Status } from '@/enums';
import { LeaveOfType, RelType } from '@/enums/leave';
import i18n from '@/plugins/i18n';
import {
  AlertCircle,
  Baby,
  CheckCircle,
  Clock,
  Heart,
  Plane,
  UserX,
  XCircle,
  type LucideIcon,
} from 'lucide-vue-next';

const statusIconMap: Record<Status, LucideIcon> = {
  [Status.PENDING]: AlertCircle,
  [Status.APPROVED]: CheckCircle,
  [Status.REJECTED]: XCircle,
};

const statusTextMap: Record<Status, string> = {
  [Status.PENDING]: i18n.global.t('common.status.pending'),
  [Status.APPROVED]: i18n.global.t('common.status.approved'),
  [Status.REJECTED]: i18n.global.t('common.status.rejected'),
};

const getStatusIcon = (status: Status) => statusIconMap[status];

const getStatusText = (status: Status) => statusTextMap[status];

const leaveTypeMap: Record<LeaveOfType, LucideIcon> = {
  [LeaveOfType.ANNUAL_LEAVE]: Plane,
  [LeaveOfType.SICK_LEAVE]: Heart,
  [LeaveOfType.UNPAID_LEAVE]: Heart,
  [LeaveOfType.MATERNITY_LEAVE]: Baby,
};

const leaveTypeTextMap: Record<LeaveOfType, string> = {
  [LeaveOfType.SICK_LEAVE]: i18n.global.t('leaves.leave_type.SICK_LEAVE'),
  [LeaveOfType.MATERNITY_LEAVE]: i18n.global.t(
    'leaves.leave_type.MATERNITY_LEAVE',
  ),
  [LeaveOfType.UNPAID_LEAVE]: i18n.global.t('leaves.leave_type.UNPAID_LEAVE'),
  [LeaveOfType.ANNUAL_LEAVE]: i18n.global.t('leaves.leave_type.ANNUAL_LEAVE'),
};

const leaveTypeClassMap: Record<LeaveOfType, string> = {
  [LeaveOfType.ANNUAL_LEAVE]:
    'bg-c-secondary/15 text-c-secondary border border-c-secondary',
  [LeaveOfType.SICK_LEAVE]:
    'bg-c-primary/15 text-c-primary border border-c-primary',
  [LeaveOfType.UNPAID_LEAVE]:
    'bg-purple-600/15 text-purple-600 border border-purple-600',
  [LeaveOfType.MATERNITY_LEAVE]:
    'bg-pink-600/15 text-pink-600 border border-pink-600',
};

const relTypeMap: Record<RelType, LucideIcon> = {
  [RelType.LEAVE]: UserX,
  [RelType.LATE]: Clock,
  [RelType.EARLY]: Clock,
};

const relTypeTextMap: Record<RelType, string> = {
  [RelType.LEAVE]: i18n.global.t('leaves.rel_type.LEAVE'),
  [RelType.LATE]: i18n.global.t('leaves.rel_type.LATE'),
  [RelType.EARLY]: i18n.global.t('leaves.rel_type.EARLY'),
};

const relTypeClassMap: Record<RelType, string> = {
  [RelType.LEAVE]: 'bg-red-500/15 text-red-500 border border-red-500',
  [RelType.LATE]: 'bg-orange-500/15 text-orange-500 border border-orange-500',
  [RelType.EARLY]: 'bg-yellow-500/15 text-yellow-500 border border-yellow-500',
};

const getLeaveTypeIcon = (type: LeaveOfType) => leaveTypeMap[type];

const getLeaveTypeClass = (type: LeaveOfType) => leaveTypeClassMap[type];

const getRelTypeIcon = (type: RelType) => relTypeMap[type];

const getRelTypeText = (type: RelType) => relTypeTextMap[type];

const getRelTypeClass = (type: RelType) => relTypeClassMap[type];

const statusClassMap: Record<Status, string> = {
  [Status.PENDING]: 'bg-amber-500/15 text-amber-500 border border-amber-500',
  [Status.APPROVED]:
    'bg-emerald-500/15 text-emerald-500 border border-emerald-500',
  [Status.REJECTED]:
    'bg-destructive/15 text-destructive border border-destructive',
};

const getStatusClass = (status: Status) =>
  statusClassMap[status] ||
  'bg-gray-600/15 text-gray-600 border border-gray-600';

const formatLeaveType = (type: LeaveOfType | number) => {
  return leaveTypeTextMap[type as LeaveOfType];
};

const formatRelType = (type: RelType | number) => {
  return relTypeTextMap[type as RelType];
};


const getRequestDisplayInfo = (item: any) => {
  const relType = item.rel_type as RelType;

  if (relType === RelType.LEAVE) {
    return {
      title: formatLeaveType(item.type_of_leave),
      class: getLeaveTypeClass(item.type_of_leave),
      icon: getLeaveTypeIcon(item.type_of_leave),
    };
  } else {
    return {
      title: getRelTypeText(relType),
      class: getRelTypeClass(relType),
      icon: getRelTypeIcon(relType),
    };
  }
};

export {
  formatLeaveType,
  getLeaveTypeClass,
  getLeaveTypeIcon,
  getRelTypeClass,
  getRelTypeIcon,
  getRelTypeText,
  getRequestDisplayInfo,
  getStatusClass,
  getStatusIcon,
  getStatusText,
  formatRelType
};
