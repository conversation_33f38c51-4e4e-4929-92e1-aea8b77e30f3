<script setup lang="ts">
import { IonIcon } from '@ionic/vue';
import {
  checkmarkCircleOutline,
  closeCircleOutline,
  warningOutline,
} from 'ionicons/icons';
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();

// Fake data for expense summary
const expenseSummary = ref({
  totalAmount: 2450000,
  pending: 750000,
  approved: 1500000,
  rejected: 200000,
});

// Format currency
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND',
  }).format(amount);
};
</script>

<template>
  <div class="flex w-full flex-col gap-4 px-0.5">
    <h4 class="text-lg font-bold text-gray-800 capitalize">
      {{ t('expense.summary') }}
    </h4>
    <div
      class="shadow-shadow-3 flex flex-col gap-4 rounded-lg bg-white px-3.5 py-3"
    >
      <div class="flex flex-col gap-1.5">
        <span class="text-sm leading-4 font-medium text-gray-600">{{
          t('expense.total_amount')
        }}</span>
        <span class="leading-5 font-bold text-gray-800">
          {{ formatCurrency(expenseSummary.totalAmount) }}
        </span>
      </div>

      <div class="flex flex-wrap items-center justify-between gap-2">
        <div class="flex items-center justify-center gap-1">
          <span class="text-sm leading-5 font-medium text-gray-600">{{
            t('expense.pending')
          }}</span>
          <IonIcon :icon="warningOutline" class="text-lg text-yellow-400" />
          <span class="text-sm font-semibold text-gray-500">
            {{ formatCurrency(expenseSummary.pending) }}
          </span>
        </div>
        <div class="flex items-center justify-center gap-1">
          <span class="text-sm leading-5 font-medium text-gray-600">
            {{ t('expense.approved') }}
          </span>
          <IonIcon
            :icon="checkmarkCircleOutline"
            class="text-lg text-green-500"
          />
          <span class="text-sm font-semibold text-gray-500">
            {{ formatCurrency(expenseSummary.approved) }}
          </span>
        </div>
        <div class="flex items-center justify-center gap-1">
          <span class="text-sm leading-5 font-medium text-gray-600">{{
            t('expense.rejected')
          }}</span>
          <IonIcon :icon="closeCircleOutline" class="text-lg text-red-500" />
          <span class="text-sm font-semibold text-gray-500">
            {{ formatCurrency(expenseSummary.rejected) }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>
