<!-- eslint-disable no-unused-vars -->
<script lang="ts" setup>
import { useLocale } from '@/composables/useLocale';
import { useProfile } from '@/composables/useProfile';
import type { Language } from '@/constants/language-list';
import { SUPPORTED_LOCALES, type SupportedLocale } from '@/constants/locales';
import { setLocaleFromLanguage } from '@/utils/set-locale';
import { useStorage } from '@vueuse/core';
import { ChevronDown } from 'lucide-vue-next';
import { computed, onMounted, onUnmounted, ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';

const props = defineProps<{
  languages: Language[];
  customClass?: string;
  showName?: boolean;
}>();

const showName = computed(() => props.showName ?? true);
const { user, modifiedLanguage } = useProfile();
const { changeLocale } = useLocale();

const emit = defineEmits<{
  (e: 'languageChange', code: string): void;
}>();

const route = useRoute();
const router = useRouter();

const isOpen = ref(false);
const dropdownRef = ref<HTMLElement | null>(null);
const language = useStorage<string>('locale', 'en');

onMounted(() => {
  const routeLocale = getLocaleFromPath(route.path);
  const userLang = user.value?.language;

  if (routeLocale) {
    language.value = routeLocale;
  } else if (userLang) {
    const langObj = props.languages.find((l) => l.language === userLang);
    language.value = langObj?.code || 'en';
  } else {

  }

  if (!routeLocale && language.value) {
    navigateToLocalizedRoute(language.value);
  }
});

watch(
  () => user.value?.language,
  (newLang) => {
    if (newLang) {
      const langObj = props.languages.find((l) => l.language === newLang);
      if (langObj) {
        language.value = langObj.code;
        navigateToLocalizedRoute(langObj.code);
      }
    }
  },
);

const selectedLanguage = computed(() => {
  return (
    (props.languages || []).find((lang) => lang.code === language.value) || {
      code: 'en',
      name: 'English',
      flagSrc: '/images/flags/en.png',
    }
  );
});

function getLocaleFromPath(path: string): SupportedLocale | null {
  const pathParts = path.split('/').filter(Boolean);
  const firstPart = pathParts[0];

  if (firstPart && SUPPORTED_LOCALES.includes(firstPart as SupportedLocale)) {
    return firstPart as SupportedLocale;
  }

  return null;
}

function navigateToLocalizedRoute(locale: string) {
  if (!SUPPORTED_LOCALES.includes(locale as SupportedLocale)) return;

  let path = route.path;
  const currentLocale = getLocaleFromPath(path);

  if (currentLocale) {
    const pathParts = path.split('/').filter(Boolean);
    pathParts.shift();
    path = '/' + pathParts.join('/');
  }

  const newPath = `/${locale}${path === '/' ? '' : path}`;

  router.replace({
    path: newPath,
    query: route.query,
    hash: route.hash,
  });
}

const selectLanguage = async (lang: Language) => {
  isOpen.value = false;

  changeLocale(lang.code as SupportedLocale);
  language.value = lang.code;

  const result = await modifiedLanguage(lang.language);
  if (!result.success) {
    setLocaleFromLanguage(lang.language);
  }

  emit('languageChange', lang.code);
};

const handleClickOutside = (e: MouseEvent) => {
  if (dropdownRef.value && !dropdownRef.value.contains(e.target as Node)) {
    isOpen.value = false;
  }
};

onMounted(() => {
  document.addEventListener('click', handleClickOutside);
});
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});
</script>

<template>
  <div class="relative" :class="customClass" ref="dropdownRef">
    <button type="button"
      class="flex h-10 w-full items-center gap-2 !rounded-md !border !border-gray-300 !px-3 !py-1.5 transition hover:bg-gray-100"
      @click="isOpen = !isOpen">
      <img :src="selectedLanguage.flagSrc || '/placeholder.svg'" :alt="selectedLanguage.name"
        class="h-5 w-5 rounded-full object-cover" />
      <span class="flex-1 text-left text-sm font-medium text-blue-600">
        {{
          showName ? selectedLanguage.name : selectedLanguage.code.toUpperCase()
        }}
      </span>
      <ChevronDown class="h-4 w-4 text-blue-600 transition-transform" :class="{ 'rotate-180': isOpen }" />
    </button>

    <transition name="fade-slide">
      <div v-if="isOpen" class="absolute z-10 mt-2 w-full rounded-md border border-gray-200 bg-white shadow-lg">
        <button v-for="lang in languages" :key="lang.code" @click="selectLanguage(lang)"
          class="flex w-full items-center gap-2 !px-4 !py-2 text-left text-sm text-gray-700 hover:bg-gray-100">
          <img :src="lang.flagSrc || '/placeholder.svg'" :alt="lang.name" class="h-5 w-5 rounded-full object-cover" />
          {{ showName ? lang.name : lang.code.toUpperCase() }}
        </button>
      </div>
    </transition>
  </div>
</template>

<style scoped>
.fade-slide-enter-active,
.fade-slide-leave-active {
  transition:
    opacity 0.2s ease,
    transform 0.2s ease;
}

.fade-slide-enter-from,
.fade-slide-leave-to {
  opacity: 0;
  transform: translateY(-5px);
}
</style>
