#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

echo "📝 Checking commit message format..."

# Use commitlint to validate commit message
npx commitlint --edit "$1"

if [ $? -eq 0 ]; then
    echo "✅ Commit message format is valid!"
else
    echo ""
    echo "💡 Commit message examples:"
    echo "  feat(auth): add login functionality"
    echo "  fix: resolve navigation bug"
    echo "  docs(readme): update installation guide"
    echo "  style: format code with prettier"
    echo "  refactor(api): restructure user service"
    echo "  test: add unit tests for auth module"
    echo "  chore: update dependencies"
    echo ""
    exit 1
fi
