#!/bin/bash

# Script kiểm tra tiếng Việt trong các file .ts, .js, .vue (bỏ qua node_modules, dist, mocks)

DIRECTORY=${1:-.}

VIETNAMESE_REGEX='[ÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚÝàáâãèéêìíòóôõùúýĂăĐđĨĩŨũƠơƯưẠạẢảẤấẦầẨẩẪẫẬậẮắẰằẲẳẴẵẶặẸẹẺẻẼẽẾếỀềỂểỄễỆệỈỉỊịỌọỎỏỐốỒồỔổỖỗỘộỚớỜờỞởỠỡỢợỤụỦủỨứỪừỬửỮữỰựỲỳỴỵỶỷỸỹ]'

FOUND_VIETNAMESE=false

echo "🔍 Đang kiểm tra các tệp .ts, .js, .vue trong: $DIRECTORY (bỏ qua node_modules, dist, mocks)"

find "$DIRECTORY" -type f \( -name "*.ts" -o -name "*.js" -o -name "*.vue" \) \
    ! -path "*/node_modules/*" \
    ! -path "*/dist/*" \
    ! -path "*/mocks/*" | while read -r file; do

    if grep -E "$VIETNAMESE_REGEX" "$file" > /dev/null; then
        echo "🚨 Tệp chứa tiếng Việt: $file"
        grep -n -E "$VIETNAMESE_REGEX" "$file" | while read -r line; do
            echo "  👉 Dòng $line"
        done
        FOUND_VIETNAMESE=true
    fi
done

if [ "$FOUND_VIETNAMESE" = false ]; then
    echo "✅ Không phát hiện tiếng Việt trong code."
fi

echo "✅ Hoàn tất kiểm tra."
