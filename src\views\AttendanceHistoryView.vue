<script setup lang="ts">
import AttendanceList from '@/components/attendance/AttendanceList.vue';
import FilterSection from '@/components/attendance/FilterSection.vue';
import SummaryStats from '@/components/attendance/SummaryStats.vue';
import { Pagination } from '@/components/ui/pagination';
import { useAttendance } from '@/composables/useAttendance';
import {
  getDayOfWeek,
  getTypeClass,
  getTypeText,
} from '@/constants/attendance-history';
import { useHeaderActionsStore } from '@/stores/header-actions';
import { IonPage } from '@ionic/vue';
import { Filter } from 'lucide-vue-next';
import { computed, h, onMounted, onUnmounted, ref, watch } from 'vue';

const headerActionsStore = useHeaderActionsStore();
const { attendanceLogsQuery } = useAttendance();

const showFilter = ref(false);
const filterFrom = ref('');
const filterTo = ref('');
const currentPage = ref(1);
const itemsPerPage = ref(10);

// Setup filters for API
const apiFilters = computed(() => ({
  date_from: filterFrom.value || undefined,
  date_to: filterTo.value || undefined,
  page: currentPage.value,
  limit: itemsPerPage.value,
}));

// Fetch data using composable
const attendanceQuery = attendanceLogsQuery(apiFilters);

// Computed values from API response
const attendanceData = computed(() => attendanceQuery.data.value?.data || []);
const pagination = computed(() => attendanceQuery.data.value?.pagination);
const loading = computed(() => attendanceQuery.isLoading.value);

// Stats calculations
const totalDays = computed(
  () =>
    attendanceData.value.filter((record) => record.time_in && record.time_out)
      .length,
);

const lateDays = computed(() => {
  // You can implement late detection logic here based on your business rules
  return 0; // Placeholder
});

const absentDays = computed(
  () =>
    attendanceData.value.filter((record) => !record.time_in || !record.time_out)
      .length,
);

const totalPages = computed(() => pagination.value?.total_pages || 1);
const totalRecords = computed(() => pagination.value?.total_items || 0);

const goToPage = (page: number) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page;
  }
};

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    goToPage(currentPage.value + 1);
  }
};

const prevPage = () => {
  if (currentPage.value > 1) {
    goToPage(currentPage.value - 1);
  }
};

const resetPagination = () => {
  currentPage.value = 1;
};

watch([filterFrom, filterTo], () => {
  resetPagination();
});

onMounted(() => {
  filterFrom.value = '';
  filterTo.value = '';
});

onMounted(() => {
  headerActionsStore.set(() =>
    h(
      'button',
      {
        class:
          'p-2 transition-colors hover:bg-gray-100 cursor-pointer rounded-md',
        onClick: () => {
          showFilter.value = !showFilter.value;
        },
      },
      [h(Filter, { class: 'size-5 text-gray-600' })],
    ),
  );
});

onUnmounted(() => {
  headerActionsStore.clear();
});
</script>

<template>
  <IonPage>
    <FilterSection
      v-model:filterFrom="filterFrom"
      v-bind:filterTo="filterTo"
      :showFilter="showFilter"
      :onToggleFilter="() => (showFilter = !showFilter)"
      :onCloseFilter="() => (showFilter = false)"
    />

    <div
      class="scroll-container flex h-full flex-col items-center gap-y-5 bg-gray-50 px-4"
    >
      <SummaryStats
        :totalDays="totalDays"
        :lateDays="lateDays"
        :absentDays="absentDays"
      />

      <AttendanceList
        :filteredRecords="attendanceData"
        :getDayOfWeek="getDayOfWeek"
        :getTypeClass="getTypeClass"
        :getTypeText="getTypeText"
      />

      <Pagination
        v-if="totalPages > 1"
        :totalPages="totalPages"
        :currentPage="currentPage"
        :totalRecords="totalRecords"
        :loading="loading"
        @prevPage="prevPage"
        @nextPage="nextPage"
        @goToPage="goToPage"
      />
    </div>
  </IonPage>
</template>

<style scoped>
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.3s ease;
}
.slide-fade-enter-from {
  opacity: 0;
  transform: translateY(-20px);
  max-height: 0;
}
.slide-fade-enter-to {
  opacity: 1;
  transform: translateY(0);
  max-height: 500px;
}
.slide-fade-leave-from {
  opacity: 1;
  transform: translateY(0);
  max-height: 500px;
}
.slide-fade-leave-to {
  opacity: 0;
  transform: translateY(-20px);
  max-height: 0;
}
</style>
