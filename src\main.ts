import '@ionic/vue/css/core.css';
import '@ionic/vue/css/display.css';
import '@ionic/vue/css/flex-utils.css';
import '@ionic/vue/css/float-elements.css';
import '@ionic/vue/css/padding.css';
import '@ionic/vue/css/structure.css';
import '@ionic/vue/css/text-alignment.css';
import '@ionic/vue/css/text-transformation.css';
import './assets/main.css';
import './registerSW';

import { QueryClient, VueQueryPlugin } from '@tanstack/vue-query';

import naiveUi from '@/plugins/naive-ui';
import pinia from '@/plugins/pinia';
import VueCookiesPlugin from '@/plugins/vue-cookies';
import { IonicVue } from '@ionic/vue';
import { createPinia } from 'pinia';
import { createApp } from 'vue';
import App from './App.vue';
import directives from './directives';
import i18n from './plugins/i18n';
import router from './router';

// Create Vue application
const app = createApp(App);

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: Infinity,
      refetchOnWindowFocus: false,
      refetchOnReconnect: false,
      refetchInterval: false,
      retry: false,
    },
  },
});

// Register plugins
app.use(createPinia());
app.use(IonicVue, {
  mode: 'md',
});
app.use(router);
app.use(i18n);
app.use(directives);
app.use(VueQueryPlugin, { queryClient });
app.use(naiveUi);
app.use(pinia);
app.use(VueCookiesPlugin);

app.mount('#app');

const appElement = document.getElementById('app');

if (appElement) {
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'attributes') {
        if (
          mutation.attributeName === 'aria-hidden' ||
          mutation.attributeName === 'data-aria-hidden'
        ) {
          appElement.removeAttribute('aria-hidden');
          appElement.removeAttribute('data-aria-hidden');
        }
      }
    });
  });

  observer.observe(appElement, {
    attributes: true,
    attributeFilter: ['aria-hidden', 'data-aria-hidden'],
  });

  appElement.removeAttribute('aria-hidden');
  appElement.removeAttribute('data-aria-hidden');
}
