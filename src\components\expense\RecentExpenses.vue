<script setup lang="ts">
import { NTag } from 'naive-ui';
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();

// Fake data for recent expenses
const recentExpenses = ref([
  {
    id: 1,
    title: 'Business Trip to Hanoi',
    date: '2023-11-15',
    amount: 1200000,
    status: 'approved',
    category: 'Travel',
  },
  {
    id: 2,
    title: 'Office Supplies',
    date: '2023-11-20',
    amount: 750000,
    status: 'pending',
    category: 'Office',
  },
  {
    id: 3,
    title: 'Client Meeting Lunch',
    date: '2023-11-25',
    amount: 500000,
    status: 'pending',
    category: 'Meals',
  },
]);

// Check if there are expenses
const hasExpenses = ref(recentExpenses.value.length > 0);

// Format currency
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND',
  }).format(amount);
};

function getStatusType(status: string) {
  switch (status.toLowerCase()) {
    case 'approved':
      return 'success';
    case 'rejected':
      return 'error';
    case 'pending':
      return 'warning';
    default:
      return 'default';
  }
}
</script>

<template>
  <div class="w-full">
    <h4 class="mb-2 text-lg font-semibold text-gray-800">
      {{ t('expense.recent_expenses') }}
    </h4>
    <div
      v-if="!hasExpenses"
      class="flex flex-col items-center rounded p-5 text-sm text-gray-600"
    >
      {{ t('expense.no_expenses') }}
    </div>
    <div v-else class="mt-3 flex flex-col gap-3">
      <div
        v-for="expense in recentExpenses"
        :key="expense.id"
        class="flex flex-col gap-1 rounded-md border border-gray-200 p-3"
      >
        <div class="flex items-center justify-between">
          <span class="font-medium">{{ expense.title }}</span>
          <n-tag :type="getStatusType(expense.status)" size="small" round>
            {{ t(`expense.${expense.status}`) }}
          </n-tag>
        </div>
        <div class="text-xs text-gray-500">{{ expense.date }}</div>
        <div class="mt-1 flex items-center justify-between">
          <span class="text-xs text-gray-500">{{ expense.category }}</span>
          <span class="text-sm font-medium">
            {{ formatCurrency(expense.amount) }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>
