import i18n from '@/plugins/i18n';
import { toast } from 'vue-sonner';

export async function registerServiceWorkerOnce() {
  if (!('serviceWorker' in navigator)) {
    toast.warning(i18n.global.t('common.firebase.unsupported'));
    return null;
  }

  if (navigator.serviceWorker.controller) {
    return navigator.serviceWorker.ready;
  }

  try {
    const registration = await navigator.serviceWorker.register(
      '/firebase-messaging-sw.js',
    );

    return registration;
  } catch (error) {
    console.error(i18n.global.t('common.firebase.register_fail', { error }));
    return null;
  }
}
