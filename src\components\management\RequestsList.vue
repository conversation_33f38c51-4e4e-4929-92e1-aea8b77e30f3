<script setup lang="ts">
import { Inbox } from 'lucide-vue-next';
import { NEmpty } from 'naive-ui';
import { useI18n } from 'vue-i18n';
import RequestCard from './RequestCard.vue';

interface Request {
  id: number;
  employeeName: string;
  type: string;
  details: string;
  date: string;
  status: string;
  staff_id: number;
  rejectionReason?: string;
}

interface Props {
  requests: Request[];
}

interface Emits {
  (e: 'approve', id: number): void;
  (e: 'reject', id: number): void;
  (e: 'reset', id: number): void;
}

defineProps<Props>();
defineEmits<Emits>();

const { t } = useI18n();
</script>

<template>
  <div v-if="requests.length > 0" class="space-y-4 px-4">
    <RequestCard
      v-for="request in requests"
      :key="request.id"
      :request="request"
      @approve="$emit('approve', $event)"
      @reject="$emit('reject', $event)"
      @reset="$emit('reset', $event)"
    />
  </div>
  <n-empty
    v-else
    :description="t('management.requests.no_requests')"
    class="py-12"
  >
    <template #icon>
      <Inbox class="h-16 w-16" />
    </template>
  </n-empty>
</template>
