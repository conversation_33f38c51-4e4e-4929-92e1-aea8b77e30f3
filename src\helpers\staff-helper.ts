import { RoleName } from '../enums/role';

/**
 *Check if the Role is a manager
 *@Param Role -Role to check
 *@Returns True if it is a manager, false if not
 */
export const isManager = (role: string): boolean => {
  return role.toUpperCase() === RoleName.MANAGER.toUpperCase();
};

/**
 *Check if the Role is Employee
 *@Param Role -Role to check
 *@Returns True if it is Employee, False if not
 */
export const isEmployee = (role: string): boolean => {
  return role.toUpperCase() === RoleName.EMPLOYEE.toUpperCase();
};

/**
 *Check if the Role is valid (of Enum Rolename)
 *@Param Role -Role to check
 *@returns true if the role is valid, false otherwise
 */
export const isValidRole = (role: string): boolean => {
  if (!role) return false;

  return Object.values(RoleName)
    .map((r) => r.toUpperCase())
    .includes(role.toUpperCase());
};
/**
 *Take all the available roles
 *@returns array contains all the roles
 */
export const getAllRoles = (): RoleName[] => {
  return Object.values(RoleName);
};

/**
 *Check access based on Role
 *@Param Userrole -Role of the current user
 *@Param RequiredRole -Role requires a minimum
 *@Returns True if you have access, false if not
 */
export const hasPermission = (
  userRole: string,
  requiredRole: RoleName,
): boolean => {
  if (!isValidRole(userRole)) return false;

  const normalizedUserRole = userRole.toUpperCase();
  const normalizedRequiredRole = requiredRole.toUpperCase();

  if (normalizedRequiredRole === RoleName.EMPLOYEE.toUpperCase()) {
    return (
      normalizedUserRole === RoleName.MANAGER.toUpperCase() ||
      normalizedUserRole === RoleName.EMPLOYEE.toUpperCase()
    );
  }

  if (normalizedRequiredRole === RoleName.MANAGER.toUpperCase()) {
    return normalizedUserRole === RoleName.MANAGER.toUpperCase();
  }

  return false;
};
