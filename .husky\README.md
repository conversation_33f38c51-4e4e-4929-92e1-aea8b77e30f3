# Husky Git Hooks

This directory contains Git hooks managed by <PERSON><PERSON> to ensure code quality.

## Available hooks:

### 🔍 pre-commit
- **Purpose**: Runs before committing
- **Functions**:
  - Runs `npm run lint` to check for code errors
  - Runs `npm run build-only` to ensure code compiles successfully (without type-check)
- **Note**: If lint or build fails, commit will be rejected

### 📝 commit-msg
- **Purpose**: Validates commit message format using commitlint
- **Tool**: Uses `@commitlint/config-conventional`
- **Required format**: `type(scope): description`
- **Valid types**: feat, fix, docs, style, refactor, test, chore, perf, ci, build, revert
- **Examples**:
  - `feat(auth): add login functionality`
  - `fix: resolve navigation bug`
  - `docs(readme): update installation guide`
  - `chore: update dependencies`

### 🚀 pre-push
- **Purpose**: Runs before pushing code to remote
- **Function**: Runs `npm run type-check` to check TypeScript errors
- **Note**: Only warns, doesn't block push

## How to skip hooks (not recommended):

```bash
# Skip pre-commit and commit-msg
git commit --no-verify -m "your message"

# Skip pre-push
git push --no-verify
```

## How to update hooks:

1. Edit the corresponding hook file in `.husky/` directory
2. Ensure file has execute permission: `chmod +x .husky/hook-name`
3. Test hook: `npx husky .husky/hook-name`
