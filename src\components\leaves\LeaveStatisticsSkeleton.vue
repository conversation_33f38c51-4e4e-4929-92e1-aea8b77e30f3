<script setup lang="ts">
import { Skeleton } from '@/components/ui/skeleton';
</script>

<template>
  <div class="grid w-full grid-cols-4 items-center gap-2.5">
    <div
      v-for="i in 4"
      :key="i"
      class="flex flex-col items-center gap-1 rounded-md border border-gray-200 px-2.5 py-2"
    >
      <Skeleton class="h-8 w-8 rounded-md" />
      <Skeleton class="h-4 w-16 rounded-md" />
    </div>
  </div>
</template>
