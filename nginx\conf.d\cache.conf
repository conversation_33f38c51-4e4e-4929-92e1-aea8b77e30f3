# Cache configuration for static assets
# This file contains optimized caching rules for different file types

# Cache for JavaScript and CSS files
location ~* \.(js|css)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
    add_header Vary "Accept-Encoding";
    
    # Enable gzip for these files
    gzip_static on;
    
    # Add ETag for better cache validation
    etag on;
}

# Cache for images
location ~* \.(png|jpg|jpeg|gif|webp|svg|ico)$ {
    expires 30d;
    add_header Cache-Control "public";
    add_header Vary "Accept-Encoding";
    
    # Optimize image delivery
    location ~* \.(svg)$ {
        add_header Content-Type image/svg+xml;
        gzip_static on;
    }
}

# Cache for fonts
location ~* \.(woff|woff2|ttf|eot|otf)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
    add_header Access-Control-Allow-Origin "*";
    add_header Vary "Accept-Encoding";
}

# Cache for media files
location ~* \.(mp4|mp3|webm|ogg|avi|mov)$ {
    expires 30d;
    add_header Cache-Control "public";
    
    # Enable range requests for media
    add_header Accept-Ranges bytes;
}

# HTML files caching is handled in the main server block

# Cache for JSON files (API responses, if served statically)
location ~* \.(json)$ {
    expires 1h;
    add_header Cache-Control "public";
    add_header Vary "Accept-Encoding";
}

# Cache for XML files
location ~* \.(xml|rss|atom)$ {
    expires 1h;
    add_header Cache-Control "public";
    add_header Vary "Accept-Encoding";
}
