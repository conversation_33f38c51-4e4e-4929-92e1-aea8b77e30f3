<!-- eslint-disable no-unused-vars -->
<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { useDateFormats } from '@/composables/useDateFormats';
import { leaveTypes, statuses } from '@/constants';
import { X } from 'lucide-vue-next';
import { NDatePicker } from 'naive-ui';
import { useI18n } from 'vue-i18n';

interface FilterData {
  type: string;
  status: string;
  fromDate: string;
  toDate: string;
}

interface Props {
  show: boolean;
  filters: FilterData;
}

interface Emits {
  (e: 'update:show', value: boolean): void;
  (e: 'update:filters', value: FilterData): void;
  (e: 'clear-filters'): void;
  (e: 'apply-filters'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();
const { t } = useI18n();
const { dateFormats } = useDateFormats();

const closeFilter = () => {
  emit('update:show', false);
};

const updateType = (value: unknown) => {
  emit('update:filters', {
    ...props.filters,
    type: value !== null && value !== undefined ? String(value) : '',
  });
};

const updateStatus = (value: unknown) => {
  emit('update:filters', {
    ...props.filters,
    status: value !== null && value !== undefined ? String(value) : '',
  });
};

const updateFromDate = (value: string | null) => {
  emit('update:filters', {
    ...props.filters,
    fromDate: value || '',
  });
};

const updateToDate = (value: string | null) => {
  emit('update:filters', {
    ...props.filters,
    toDate: value || '',
  });
};

const clearFilters = () => {
  emit('clear-filters');
};

const applyFilters = () => {
  emit('apply-filters');
};
</script>

<template>
  <Transition name="slide-fade">
    <div v-if="show" class="w-full rounded-md border bg-white p-3 shadow">
      <div class="flex items-center justify-between">
        <span class="text-lg font-semibold">{{
          t('leaves.filter.title')
        }}</span>
        <button
          class="hover:text-c-primary hover:bg-c-primary/20 cursor-pointer rounded p-0.5 text-gray-600"
          @click="closeFilter"
        >
          <X class="size-5" />
        </button>
      </div>

      <Separator class="my-3" />

      <div class="grid grid-cols-2 gap-3">
        <div class="space-y-2">
          <Label>{{ t('leaves.filter.leave_type') }}</Label>
          <Select
            :model-value="filters.type || undefined"
            @update:model-value="updateType"
          >
            <SelectTrigger class="w-full">
              <SelectValue :placeholder="t('leaves.filter.all_types')" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem
                v-for="item in leaveTypes"
                :key="item.value"
                :value="item.value"
              >
                {{ item.label }}
              </SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div class="space-y-2">
          <Label>{{ t('leaves.filter.all_status') }}</Label>
          <Select
            :model-value="filters.status || undefined"
            @update:model-value="updateStatus"
          >
            <SelectTrigger class="w-full">
              <SelectValue :placeholder="t('leaves.filter.all_status')" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem
                v-for="item in statuses"
                :key="item.value"
                :value="item.value"
              >
                {{ item.label }}
              </SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div class="space-y-2">
          <Label>{{ t('leaves.filter.start_date') }}</Label>
          <n-date-picker
            :formatted-value="filters.fromDate || null"
            @update:formatted-value="updateFromDate"
            :value-format="dateFormats.date.value"
            type="date"
            clearable
            :placeholder="dateFormats.date.display"
          />
        </div>
        <div class="space-y-2">
          <Label>{{ t('leaves.filter.end_date') }}</Label>
          <n-date-picker
            :formatted-value="filters.toDate || null"
            @update:formatted-value="updateToDate"
            :value-format="dateFormats.date.value"
            type="date"
            clearable
            :placeholder="dateFormats.date.display"
          />
        </div>
        <div class="col-span-2 grid grid-cols-2 gap-4">
          <Button variant="outline-destructive" @click="clearFilters">
            {{ t('leaves.filter.clear') }}
          </Button>
          <Button variant="blue" @click="applyFilters">
            {{ t('leaves.filter.apply') }}
          </Button>
        </div>
      </div>
    </div>
  </Transition>
</template>

<style scoped>
.slide-fade-enter-active {
  transition:
    opacity 0.3s ease-out,
    transform 0.3s cubic-bezier(0.22, 1, 0.36, 1);
}

.slide-fade-leave-active {
  transition:
    opacity 0.25s ease-in,
    transform 0.25s ease-in;
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

.slide-fade-enter-active,
.slide-fade-leave-active {
  will-change: opacity, transform;
}
</style>
