export const getVisiblePages = (
  currentPage: number,
  totalPages: number,
  maxVisible: number = 5,
): (number | string)[] => {
  const pages: (number | string)[] = [];
  const half = Math.floor(maxVisible / 2);

  let start = Math.max(1, currentPage - half);
  let end = Math.min(totalPages, currentPage + half);

  if (end - start + 1 < maxVisible) {
    if (start === 1) {
      end = Math.min(totalPages, start + maxVisible - 1);
    } else if (end === totalPages) {
      start = Math.max(1, end - maxVisible + 1);
    }
  }

  for (let i = start; i <= end; i++) {
    pages.push(i);
  }

  if (start > 1) pages.unshift('...');
  if (end < totalPages) pages.push('...');

  return pages;
};
