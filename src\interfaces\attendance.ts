import type { AttendanceType, ShiftType, WorkType } from "@/enums/attendance";
import type { EmployeeInfo, ManagementInfo, StaffInfo } from "./staff";

import type { Action } from "@/enums/leave";
import type { ApiResponse } from "@/types";
import type { Status } from "@/enums";

export interface Attendance {
  id: number;
  date: string;
  type?: AttendanceType;
  time_in: string;
  time_out: string;
  total_time: string;
}

// New detailed attendance interfaces based on your JSON structure
export interface CheckInOut {
  id: number;
  time: string;
  datetime: string;
  type: string;
  route_point_id: number;
  workplace_id: number;
}

export interface AttendanceDetails {
  check_ins: CheckInOut[];
  check_outs: CheckInOut[];
}

export interface AttendanceInfo {
  status: string;
  time_in: string;
  time_in_id: number;
  time_out: string;
  time_out_id: number;
  total_time: string;
  check_in_count: number;
  check_out_count: number;
  details: AttendanceDetails;
}

export interface AssignedShift {
  id: number;
  shift_code: string;
  shift_name: string;
  shift_type: string;
  department: string;
  position: string;
  time_in: string;
  time_out: string;
  total_hours: number;
  shifts_detail: string;
}

export interface ShiftSummary {
  shift_code: string;
  shift_name: string;
  shift_type: string;
  department: string;
  position: string;
  time_in: string;
  time_out: string;
  total_hours: number;
  shifts_detail: string;
}

export interface ShiftsInfo {
  assigned_shifts: AssignedShift[];
  total_shifts: number;
  shift_summary: ShiftSummary[];
}

export interface OvertimeRequestDetail {
  id: number;
  time_in: string;
  time_out: string;
  timekeeping_value: number;
  reason: string;
  status: number;
  status_text: string;
  reject_reason: string;
  registration_type: string;
  group_code: string | null;
}

export interface OvertimeInfo {
  total_hours: number;
  total_requests: number;
  approved_requests: number;
  requests: OvertimeRequestDetail[];
}

export interface LeaveInfo {
  status: string;
  info: any | null;
  total_requests: number;
}

export interface DaySummary {
  total_working_hours: string;
  total_overtime_hours: number;
  is_holiday: boolean;
  notes: string | null;
}

export interface AttendanceDay {
  id: number;
  date: string;
  day_of_week: string;
  is_weekend: boolean;
  attendance: AttendanceInfo;
  shifts: ShiftsInfo;
  overtime: OvertimeInfo;
  leave: LeaveInfo;
  summary: DaySummary;
}

export interface AttendanceSummary {
  total_days: number;
  working_days: number;
  weekend_days: number;
  days_with_attendance: number;
  absent_days: number;
  days_on_leave: number;
  days_with_shifts: number;
  total_overtime_hours: number;
  total_overtime_requests: number;
  approved_overtime_requests: number;
  total_shift_hours: number;
}

export interface FiltersApplied {
  date_from: string;
  date_to: string;
}

export interface StaffPeriod {
  from: string;
  to: string;
  total_days: number;
}

export interface StaffAttendanceInfo {
  staff_id: number;
  period: StaffPeriod;
}

export interface DetailedAttendanceResponse {
  success: boolean;
  data: AttendanceDay[];
  summary: AttendanceSummary;
  filters_applied: FiltersApplied;
  staff_info: StaffAttendanceInfo;
}

export interface WorkSchedule {
  date: Date;
  title: string;
  description: string;
  startTime: string | Date | number;
  endTime: string;
  status: 'present' | 'absent' | 'half-day' | 'on-leave';
}

export interface WorkShift {
  id: number;
  shift_code: string;
  time_start: string;
  time_end: string;
  description: string;
  segments: Segment[];
}

export interface Segment {
  start: string;
  end: string;
  description: string;
  shift_type: ShiftType;
  work_type: WorkType;
}

export interface Holiday {
  id: number;
  holiday_name: string;
  holiday_date: string;
  off_type: string;
  days_remaining: number;
  day_of_week: number;
  day_of_week_label: string;
}

export interface OvertimeRequest {
  employees: number[];
  creator?: number;
  additional_day: string;
  timekeeping_value: string;
  time_in: string;
  time_out: string;
  approver: number;
  reason: string;
}

export interface UpdateEmployeeStatusPayload {
  record_id: number;
  employee_status: number;
  reject_reason?: string;
}

export interface Overtime {
  id: number |string;
  additional_day: string;
  registration_type: string;
  time_in: string;
  time_out: string;
  timekeeping_value: number;
  reason: string;
  status: Status;
  status_text: string;
  serious_number: number;
  reject_reason: string;
  creator: number;
  approver: number;
  creator_info: StaffInfo;
  approver_info: StaffInfo | null;
  can_edit?: boolean;
  group_info?: GroupOvertimeInfo & { employees: OvertimeEmployee[] };
  employees?: OvertimeEmployee[];
}

export interface OvertimeEmployee {
  id: number;
  employee_id: number;
  employee_status: number;
  employee_status_text: string;
  employee_info: {
    staff_id: number;
    full_name: string;
    staff_identifi: string;
    email: string;
  };
}

export interface GroupOvertimeInfo {
  group_code: string;
  registration_type: string;
  additional_day: string;
  time_in: string;
  time_out: string;
  timekeeping_value: number;
  reason: string;
  status: number;
  status_text: string;
  reject_reason: string;
  session: number;
  creator: number;
  approver: number | null;
  creator_info: StaffInfo;
  approver_info: StaffInfo;
  can_approve: boolean;
  can_reject: boolean;
  created_at: string;
  can_edit: boolean;
}

export interface GroupOvertimeRequest {
  group_info: GroupOvertimeInfo & { total_employees: number };
  employees: EmployeeInfo[];
}

export interface IndividualOvertimeRequest {
  id: number;
  type: string;
  registration_type: string;
  additional_day: string;
  time_in: string;
  time_out: string;
  timekeeping_value: number;
  reason: string;
  status: number;
  status_text: string;
  reject_reason: string;
  creator: number;
  approver: number | null;
  employee_id: number;
  creator_info: StaffInfo;
  employee_info: StaffInfo;
  can_approve: boolean;
  can_reject: boolean;
  created_at: string;
}

export interface ManagementOvertimeResponse {
  success: boolean;
  data: (GroupOvertimeRequest | IndividualOvertimeRequest)[];
  pagination: Pagination;
  total: number;
  filters_applied: Record<string, any>;
  management_info: ManagementInfo;
}

export interface ApproveOvertimeRequestData {
  action: string;
  total_processed: number;
  total_failed: number;
  processed_requests: ProcessedRequest[];
  failed_requests?: FailedRequest[];
  group_code?: string;
  reject_reason?: string;
}

export interface ApproveOvertimeRequestResponse {
  success: boolean;
  message: string;
  data: ApproveOvertimeRequestData;
}

export interface ApproveOvertimeRequestPayload {
  id?: number;
  group_code?: string;
  action: Action;
  reject_reason?: string;
}

export interface ProcessedRequest {
  id: number;
  employee_name: string;
  additional_day: string;
  action: string;
  status: number;
}

export interface FailedRequest {
  id: number;
  reason: string;
}


// Bulk update requests interfaces (for leave requests)
export interface BulkUpdateRequestsPayload {
  id?: number;
  group_code?: string;
  action: Action;
  reject_reason?: string;
}

export interface BulkSuccessfulUpdate {
  id: number;
  staff_id: number;
  group_code: string | null;
  action: string;
  status: number;
  confirmation_date: string;
}

export interface BulkFailedUpdate {
  id: number;
  reason: string;
}

export interface BulkGroupSummary {
  group_code: string;
  action: string;
  processed_count: number;
  total_requests: number;
}

export interface BulkUpdateSummary {
  total_requests: number;
  successful_count: number;
  failed_count: number;
  action: string;
  confirmation_date: string;
  approver_id: number;
}

export interface BulkProcessingDetails {
  individual_requests_processed: number;
  group_requests_processed: number;
  unique_groups_affected: number;
}

export interface BulkUpdateRequestsResponse {
  success: boolean;
  message: string;
  summary: BulkUpdateSummary;
  successful_updates: BulkSuccessfulUpdate[];
  group_summaries?: BulkGroupSummary[];
  failed_updates?: BulkFailedUpdate[];
  reject_reason?: string;
  processing_details: BulkProcessingDetails;
}

export interface Pagination {
  page: number;
  total_pages: number;
  total_items: number;
  items_per_page?: number;
}

export interface PaginatedResponse<T = unknown> {
  status: boolean;
  message?: string;
  data?: T[];
  pagination?: Pagination;
  total?: number;
}

export interface OvertimeStats {
  total_mins: number;
  total_approves: number;
  total_pendings: number;
  total_requests: number;
}

export type OvertimeRequestResponse = ApiResponse<OvertimeRequest>;
export type WorkShiftResponse = ApiResponse<WorkShift[]>;
export type OvertimeResponse = PaginatedResponse<Overtime>;
export type OvertimeItemResponse = ApiResponse<Overtime>;
export type PaginatedOvertimeResponse = PaginatedResponse<Overtime>;
export type PaginatedAttendanceResponse = PaginatedResponse<Attendance>;
export type HolidayResponse = ApiResponse<Holiday[]>;
export type OvertimeStatsResponse = ApiResponse<OvertimeStats>;
export type DetailedAttendanceApiResponse = ApiResponse<DetailedAttendanceResponse>;
