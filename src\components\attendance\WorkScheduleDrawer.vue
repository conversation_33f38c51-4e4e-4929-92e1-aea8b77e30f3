<script setup lang="ts">
import { Button } from '@/components/ui/button';
import {
  <PERSON>er,
  <PERSON>er<PERSON><PERSON>nt,
  <PERSON>er<PERSON>ooter,
  <PERSON>er<PERSON>eader,
  DrawerTitle,
} from '@/components/ui/drawer';
import { useLocale } from '@/composables/useLocale';
import type { WorkSchedule } from '@/interfaces/attendance';
import { cn } from '@/lib/utils';
import { formatFullDate } from '@/utils/format';
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';

const props = defineProps<{
  open: boolean;
  selectedDay: string | number | Date | null;
  schedules: WorkSchedule[];
}>();

const emit = defineEmits(['update:open']);

const { t } = useI18n();
const { locale } = useLocale()

const hasSchedules = computed(() => props.schedules.length > 0);

const getStatusColor = (status: string) => {
  switch (status) {
    case 'present':
      return 'bg-green-100 border-green-700 border opacity-75 text-green-700';
    case 'absent':
      return 'bg-red-100 border opacity-75 border-destructive text-red-700';
    case 'half_day':
      return 'bg-yellow-100 border opacity-75 border-yellow-500 text-yellow-700';
    case 'on_leave':
      return 'bg-sky-100 border opacity-75 border-sky-500 text-sky-700';
    default:
      return 'bg-gray-100 border opacity-75 border-gray-900 text-gray-700';
  }
};

const closeDrawer = () => {
  emit('update:open', false);
};
</script>

<template>
  <Drawer :open="open" @update:open="emit('update:open', $event)">
    <DrawerContent>
      <DrawerHeader>
        <DrawerTitle>{{ formatFullDate(selectedDay, locale) }}</DrawerTitle>
      </DrawerHeader>

      <div v-if="selectedDay" class="px-4">
        <div v-if="hasSchedules" class="space-y-4">
          <div v-for="(schedule, index) in schedules" :key="index" class="rounded-lg bg-gray-50 p-4">
            <div class="mb-2 flex items-center justify-between">
              <h3 class="text-lg font-medium text-gray-900">
                {{ schedule.title }}
              </h3>
              <span :class="cn('rounded-full px-2 py-1 text-xs font-medium', getStatusColor(schedule.status))">
                <!-- {{ t(`attendance.work_schedule.${schedule.status.replace('-', '_')}`) }} -->
                {{ t(`attendance.work_schedule.${schedule.status.replace('-', '_')}`).toUpperCase() }}
              </span>
            </div>

            <div class="mt-2 flex items-center text-sm text-gray-500">
              <span class="inline-flex items-center">
                <span class="font-medium">
                  {{ t('attendance.work_schedule.details.time') }}:
                </span>
                <span class="ml-1">{{ schedule.startTime }} - {{ schedule.endTime }}</span>
              </span>
            </div>

            <div class="mt-2">
              <p class="text-gray-700">{{ schedule.description }}</p>
            </div>
          </div>
        </div>

        <div v-else class="py-8 text-center">
          <p class="text-gray-500">
            {{ t('attendance.work_schedule.details.no_schedules') }}
          </p>
        </div>
      </div>

      <DrawerFooter>
        <Button variant="destructive" class="w-full" @click="closeDrawer">
          {{ t('attendance.common.close') }}
        </Button>
      </DrawerFooter>
    </DrawerContent>
  </Drawer>
</template>
