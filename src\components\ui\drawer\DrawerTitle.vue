<script lang="ts" setup>
import { cn } from '@/lib/utils';
import type { DrawerTitleProps } from 'vaul-vue';
import { DrawerTitle } from 'vaul-vue';
import { computed, type HtmlHTMLAttributes } from 'vue';

const props = defineProps<
  DrawerTitleProps & { class?: HtmlHTMLAttributes['class'] }
>();

const delegatedProps = computed(() => {
  const { ...delegated } = props;

  return delegated;
});
</script>

<template>
  <DrawerTitle
    data-slot="drawer-title"
    v-bind="delegatedProps"
    :class="cn('text-foreground font-semibold', props.class)"
  >
    <slot />
  </DrawerTitle>
</template>
