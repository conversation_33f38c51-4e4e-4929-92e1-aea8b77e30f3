import { ATTENDANCE, HOME, LEAVES } from '@/constants/routes';
import { calendarOutline, documentOutline, homeOutline } from 'ionicons/icons';

interface TabItem {
  tab: string;
  basePath: string; // The base path without locale prefix
  icon: string;
  label: string;
}

// Define tabs with base paths (without locale prefix)
export const tabsList: TabItem[] = [
  {
    tab: 'home',
    basePath: HOME,
    icon: homeOutline,
    label: 'common.nav.home',
  },
  {
    tab: 'attendance',
    basePath: ATTENDANCE,
    icon: calendarOutline,
    label: 'common.nav.attendance',
  },
  {
    tab: 'leaves',
    basePath: LEAVES,
    icon: documentOutline,
    label: 'common.nav.leaves',
  },
  // {
  //   tab: 'expenses',
  //   basePath: EXPENSES,
  //   icon: receiptOutline,
  //   label: 'common.nav.expenses',
  // },
  // {
  //   tab: 'salary',
  //   basePath: DASHBOARD_SALARY_SLIPS,
  //   icon: briefcaseOutline,
  //   label: 'Salary',
  // },
];
