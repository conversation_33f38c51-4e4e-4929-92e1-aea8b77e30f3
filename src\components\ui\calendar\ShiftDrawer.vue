<template>
  <div
    class="fixed bottom-0 left-0 right-0 bg-white rounded-t-2xl shadow-2xl z-50 transform transition-transform duration-300"
    :class="showDrawer ? 'translate-y-0' : 'translate-y-full'" role="dialog" aria-modal="true"
    aria-labelledby="drawer-title">
    <div class="p-4">
      <div class="w-12 h-1 bg-gray-300 rounded-full mx-auto mb-4"></div>
      <div class="text-center mb-6">
        <h2 id="drawer-title" class="text-base font-bold text-gray-800">
          {{ selectedDay?.date ? formatFullDate(selectedDay.date, locale) : '' }}
        </h2>
        <p class="text-gray-500 text-sm">{{ t('attendance.details.title') }}</p>
      </div>
      <div v-if="selectedDay?.shifts?.length" class="space-y-4 max-h-60 scroll-container overflow-y-auto">
        <ShiftCard v-for="shift in selectedDay.shifts" :key="shift.code" :shift="shift" :get-shift-color="getShiftColor"
          :get-shift-type-style="getShiftTypeStyle" :calculate-duration="calculateDuration" />
      </div>
      <div v-else class="text-center py-8">
        <Calendar class="w-12 h-12 text-gray-300 mx-auto mb-2" />
        <p class="text-gray-500">{{ t('attendance.details.no_shift_available') }}</p>
      </div>
      <button @click="$emit('closeDrawer')"
        class="w-full mt-6 bg-blue-500 text-white py-2 rounded-lg font-medium hover:bg-blue-600 transition-colors">
        {{ t('attendance.common.close') }}
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Calendar } from 'lucide-vue-next'
import ShiftCard from './ShiftCard.vue'
import type { CalendarDay } from '@/interfaces/calendar'
import { formatFullDate } from '@/utils/format'
import { useI18n } from 'vue-i18n'
import { useLocale } from '@/composables/useLocale'

interface Props {
  showDrawer: boolean
  selectedDay: CalendarDay | null
  getShiftColor: (shiftCode: string) => string
  getShiftTypeStyle: (type: string) => string
  calculateDuration: (startTime: string, endTime: string) => string
}

defineProps<Props>()

interface Emits {
  (e: 'closeDrawer'): void
}
const { t } = useI18n()
const { locale } = useLocale()
defineEmits<Emits>()
</script>
