{"common": {"nav": {"home": "Home", "about": "About", "attendance": "Attendance", "leaves": "Leaves", "expenses": "Expenses", "salary": "Salary", "profile": "Profile"}, "loading": "Loading...", "error": "An error occurred", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "dismiss": "<PERSON><PERSON><PERSON>", "view": "View", "submit": "Submit", "back": "Back", "next": "Next", "confirm": "Confirm", "search": "Search", "filter": "Filter", "sort": "Sort", "logout": "Logout", "choose_file": "Choose <PERSON>", "no_file_choose": "No file chosen", "validation": {"required": "This field is required", "email": "Please enter a valid email address", "minLength": "This field must be at least {min} characters", "maxLength": "This field must be less than {max} characters"}, "not_found": {"title": "Page Not Found", "message": "The page you are looking for doesn't exist or has been moved.", "go_home": "Go Home"}, "sharp": "<PERSON>", "pagination": {"total": "Total: {total} items", "page": "Page {page} of {totalPages}"}, "alert": {"error": "Error", "error_message": "An error occurred while processing your request!", "success": "Success", "success_message": "Your request has been processed successfully!", "warning": "Warning", "warning_message": "Please check your input and try again.", "info": "Info", "info_message": "This is an informational message.", "validation_error": "Validation Error: {errors}", "form_validation_fail": "Form validation failed. Please check your input."}, "toast": {"success": {"create": "{type} request created successfully!", "update": "{type} request updated successfully!", "delete": "{type} request deleted successfully!"}, "error": {"create": "Failed to create {type} request.", "update": "Failed to update {type} request.", "delete": "Failed to delete {type} request.", "get_locale": "Failed to get current locale", "set_locale": "Failed to set locale", "load_user": "Failed to load user information", "unknown": "An unknown error occurred", "something_went_wrong": "Some thing went wrong"}}, "firebase": {"register_fail": "Service Worker registration failed: {error}", "unsupported": "Service Worker is not supported in this browser", "failed_to_get_token": "Failed to get token: {error}", "message_received": "Message received: {message}", "no_token": "No registration token available"}, "module_type": {"overtime": "Overtime", "leave": "Leave", "expense": "Expense", "salary": "Salary", "profile": "Profile"}, "weekdays": {"sunday": "Sunday", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday"}, "status": {"all": "All", "pending": "Pending", "approved": "Approved", "rejected": "Rejected"}}}