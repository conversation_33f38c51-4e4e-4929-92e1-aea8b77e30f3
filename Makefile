# Makefile for Sharp Vue.js Docker operations

# Variables
IMAGE_NAME := sharp-vue-app
TAG := latest
REGISTRY := 
COMPOSE_FILE := docker-compose.yml

# Colors
BLUE := \033[0;34m
GREEN := \033[0;32m
YELLOW := \033[1;33m
RED := \033[0;31m
NC := \033[0m # No Color

.PHONY: help build run stop restart logs clean test deploy health

# Default target
.DEFAULT_GOAL := help

help: ## Show this help message
	@echo "$(BLUE)Sharp Vue.js Docker Management$(NC)"
	@echo ""
	@echo "$(YELLOW)Available commands:$(NC)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(GREEN)%-15s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)

build: ## Build the Docker image
	@echo "$(BLUE)Building Docker image...$(NC)"
	@chmod +x docker-build.sh
	@./docker-build.sh -n $(IMAGE_NAME) -t $(TAG)

build-no-cache: ## Build the Docker image without cache
	@echo "$(BLUE)Building Docker image without cache...$(NC)"
	@docker build --no-cache -t $(IMAGE_NAME):$(TAG) .

run: ## Run the application with docker-compose
	@echo "$(BLUE)Starting application...$(NC)"
	@chmod +x docker-deploy.sh
	@./docker-deploy.sh up

run-build: ## Build and run the application
	@echo "$(BLUE)Building and starting application...$(NC)"
	@chmod +x docker-deploy.sh
	@./docker-deploy.sh build

stop: ## Stop the application
	@echo "$(BLUE)Stopping application...$(NC)"
	@docker-compose -f $(COMPOSE_FILE) down

restart: ## Restart the application
	@echo "$(BLUE)Restarting application...$(NC)"
	@docker-compose -f $(COMPOSE_FILE) restart

logs: ## Show application logs
	@echo "$(BLUE)Showing logs...$(NC)"
	@docker-compose -f $(COMPOSE_FILE) logs -f sharp-app

logs-nginx: ## Show nginx logs
	@echo "$(BLUE)Showing nginx logs...$(NC)"
	@docker-compose -f $(COMPOSE_FILE) exec sharp-app tail -f /var/log/nginx/access.log

status: ## Show container status
	@echo "$(BLUE)Container status:$(NC)"
	@docker-compose -f $(COMPOSE_FILE) ps

health: ## Check application health
	@echo "$(BLUE)Checking application health...$(NC)"
	@curl -f http://localhost:8085/health || echo "$(RED)Health check failed$(NC)"

shell: ## Access container shell
	@echo "$(BLUE)Accessing container shell...$(NC)"
	@docker-compose -f $(COMPOSE_FILE) exec sharp-app sh

nginx-test: ## Test nginx configuration
	@echo "$(BLUE)Testing nginx configuration...$(NC)"
	@docker-compose -f $(COMPOSE_FILE) exec sharp-app nginx -t

nginx-reload: ## Reload nginx configuration
	@echo "$(BLUE)Reloading nginx configuration...$(NC)"
	@docker-compose -f $(COMPOSE_FILE) exec sharp-app nginx -s reload

clean: ## Clean up Docker resources
	@echo "$(BLUE)Cleaning up Docker resources...$(NC)"
	@docker-compose -f $(COMPOSE_FILE) down -v
	@docker system prune -f
	@docker volume prune -f

clean-all: ## Clean up all Docker resources including images
	@echo "$(BLUE)Cleaning up all Docker resources...$(NC)"
	@docker-compose -f $(COMPOSE_FILE) down -v --rmi all
	@docker system prune -a -f
	@docker volume prune -f

push: ## Push image to registry
	@echo "$(BLUE)Pushing image to registry...$(NC)"
	@chmod +x docker-build.sh
	@./docker-build.sh -n $(IMAGE_NAME) -t $(TAG) -p -r $(REGISTRY)

dev: ## Run in development mode
	@echo "$(BLUE)Starting development environment...$(NC)"
	@npm run dev

install: ## Install dependencies
	@echo "$(BLUE)Installing dependencies...$(NC)"
	@npm install

lint: ## Run linting
	@echo "$(BLUE)Running linter...$(NC)"
	@npm run lint

format: ## Format code
	@echo "$(BLUE)Formatting code...$(NC)"
	@npm run format

test: ## Run tests (if available)
	@echo "$(BLUE)Running tests...$(NC)"
	@echo "$(YELLOW)No tests configured yet$(NC)"

deploy-prod: ## Deploy to production
	@echo "$(BLUE)Deploying to production...$(NC)"
	@make build TAG=production
	@make run

backup-volumes: ## Backup Docker volumes
	@echo "$(BLUE)Backing up volumes...$(NC)"
	@docker run --rm -v sharp_nginx-logs:/data -v $(PWD)/backup:/backup alpine tar czf /backup/nginx-logs-$(shell date +%Y%m%d-%H%M%S).tar.gz -C /data .

restore-volumes: ## Restore Docker volumes (specify BACKUP_FILE)
	@echo "$(BLUE)Restoring volumes from $(BACKUP_FILE)...$(NC)"
	@docker run --rm -v sharp_nginx-logs:/data -v $(PWD)/backup:/backup alpine tar xzf /backup/$(BACKUP_FILE) -C /data

monitor: ## Monitor container resources
	@echo "$(BLUE)Monitoring container resources...$(NC)"
	@docker stats

# Development helpers
setup: ## Setup development environment
	@echo "$(BLUE)Setting up development environment...$(NC)"
	@make install
	@chmod +x docker-build.sh docker-deploy.sh
	@echo "$(GREEN)Setup complete!$(NC)"

quick-start: ## Quick start for new users
	@echo "$(BLUE)Quick start...$(NC)"
	@make setup
	@make build
	@make run
	@echo "$(GREEN)Application is running at http://localhost:8085$(NC)"
