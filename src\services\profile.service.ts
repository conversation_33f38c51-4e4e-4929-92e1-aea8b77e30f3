import { LanguageValue } from '@/enums/language';
import type { User } from '@/interfaces/staff';
import apiClient from '../api/axios-instance';

export interface ProfileResponse {
  message: string;
  data: User;
}
export interface LanguageUpdateResponse {
  message: string;
}

export const getProfileUser = async (): Promise<ProfileResponse> => {
  const { data } = await apiClient.get<ProfileResponse>('/profile');
  return data;
};

export const updateLanguage = async (
  language: LanguageValue,
): Promise<LanguageUpdateResponse> => {
  const { data } = await apiClient.post('/profile/modified-language', {
    language,
  });

  return data;
};
