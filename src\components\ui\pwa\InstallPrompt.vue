<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { useVModel } from '@vueuse/core';
import { onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';

interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[];
  prompt(): Promise<void>;
  userChoice: Promise<{
    outcome: 'accepted' | 'dismissed';
    platform: string;
  }>;
}

const props = defineProps<{
  modelValue: boolean;
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void;
}>();

const showPrompt = useVModel(props, 'modelValue', emit);
const deferredPrompt = ref<BeforeInstallPromptEvent | null>(null);
const isIOS = ref(false);
const { t } = useI18n();

onMounted(() => {
  isIOS.value = /iPad|iPhone|iPod/.test(navigator.userAgent);

  window.addEventListener('beforeinstallprompt', (e) => {
    e.preventDefault();
    deferredPrompt.value = e as BeforeInstallPromptEvent;
    showPrompt.value = true;
  });
});

const installPWA = async () => {
  if (deferredPrompt.value) {
    deferredPrompt.value.prompt();
    const { outcome } = await deferredPrompt.value.userChoice;
    if (outcome === 'accepted') {
      deferredPrompt.value = null;
      showPrompt.value = false;
    }
  }
};

const closePrompt = () => {
  showPrompt.value = false;
  localStorage.setItem('pwa-prompt-dismissed', 'true');
};
</script>

<template>
  <div v-if="showPrompt" class="fixed right-0 bottom-0 left-0 z-50 p-4">
    <div
      class="mx-auto max-w-md rounded-lg bg-white p-4 shadow-lg dark:bg-gray-800"
    >
      <div class="mb-3 text-lg font-semibold">
        {{ t('pwa.install_title') }}
      </div>

      <div class="mb-4 text-sm text-gray-600 dark:text-gray-300">
        <template v-if="isIOS">
          {{ t('pwa.ios_instructions') }}
        </template>
        <template v-else>
          {{ t('pwa.android_instructions') }}
        </template>
      </div>

      <div class="flex justify-end gap-2">
        <Button variant="ghost" size="lg" @click="closePrompt">
          {{ t('common.dismiss') }}
        </Button>
        <Button size="lg" v-if="!isIOS" @click="installPWA">
          {{ t('pwa.install_now') }}
        </Button>
      </div>
    </div>
  </div>
</template>
