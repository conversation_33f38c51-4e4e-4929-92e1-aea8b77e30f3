export interface ShiftDefinition {
  name: string
  start_time: string
  end_time: string
  type: string
}

export interface RawShift {
  code: string
}

export interface Shift extends RawShift, ShiftDefinition {}

export interface CalendarDay {
  date: Date
  day: number
  is_current_month: boolean
  is_today: boolean
  has_shift: boolean
  shifts: Shift[]
  attendance_status?: "present" | "absent" | "half_day" | "on_leave"
}

export interface ShiftType {
  code: string
  description: string
  time: string
}
