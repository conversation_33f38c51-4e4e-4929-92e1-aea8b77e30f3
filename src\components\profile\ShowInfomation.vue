<script setup lang="ts">
import CompanyInformation from '@/components/profile/CompanyInformation.vue';
import ContactInformation from '@/components/profile/ContactInformation.vue';
import EmployyeeDetails from '@/components/profile/EmployyeeDetails.vue';
import SalaryInformation from '@/components/profile/SalaryInformation.vue';
import { Drawer, DrawerTrigger } from '@/components/ui/drawer';
import { IonIcon } from '@ionic/vue';
import {
  callOutline,
  cashOutline,
  documentOutline,
  personOutline,
} from 'ionicons/icons';
import { ChevronRight } from 'lucide-vue-next';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const userProfile = [
  {
    title: t('profile.employee_details'),
    icon: personOutline,
    modal: EmployyeeDetails,
  },
  {
    title: t('profile.company_information'),
    icon: documentOutline,
    modal: CompanyInformation,
  },
  {
    title: t('profile.contact_information'),
    icon: callOutline,
    modal: ContactInformation,
  },
  {
    title: t('profile.salary_information'),
    icon: cashOutline,
    modal: SalaryInformation,
  },
];
</script>

<template>
  <div class="my-4 flex w-full flex-col gap-5">
    <div class="shadow-shadow-2 flex flex-col rounded-md bg-white">
      <Drawer v-for="(item, index) in userProfile" :key="index">
        <DrawerTrigger>
          <div
            class="flex cursor-pointer items-center justify-between border-b p-4"
          >
            <div class="flex grow flex-row items-center gap-3">
              <ion-icon :icon="item.icon" :stroke-width="1.5" class="size-5" />
              <span class="text-base font-normal text-gray-800">
                {{ item.title }}
              </span>
            </div>
            <ChevronRight class="size-5" :stroke-width="2" />
          </div>
        </DrawerTrigger>
        <component :is="item.modal" />
      </Drawer>
    </div>
  </div>
</template>
