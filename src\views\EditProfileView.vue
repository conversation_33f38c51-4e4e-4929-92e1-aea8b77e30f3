<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { useDateFormats } from '@/composables/useDateFormats';
import { useHeaderActionsStore } from '@/stores/header-actions';
import { useUserStore } from '@/stores/user';
import { IonPage } from '@ionic/vue';
import { Briefcase, Camera, CreditCard, Phone, User } from 'lucide-vue-next';
import {
  NAutoComplete,
  NDatePicker,
  NForm,
  NFormItem,
  NInput,
  NSelect,
} from 'naive-ui';
import { h, onMounted, onUnmounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { toast } from 'vue-sonner';

const { t } = useI18n();
const userStore = useUserStore();
const headerActionsStore = useHeaderActionsStore();
const { dateFormats } = useDateFormats();

const genderOptions = ref([
  { value: 'male', label: t('profile.edit.male') },
  { value: 'female', label: t('profile.edit.female') },
  { value: 'other', label: t('profile.edit.other') },
]);

const educationOptions = ref([
  {
    value: 'high_school',
    label: t('profile.edit.high_school'),
  },
  {
    value: 'bachelor',
    label: t('profile.edit.bachelor'),
  },
  { value: 'master', label: t('profile.edit.master') },
  { value: 'phd', label: t('profile.edit.phd') },
  { value: 'other', label: t('profile.edit.other') },
]);

onMounted(async () => {
  try {
    await userStore.getCurrentUser();
  } catch (error) {
    toast.error(`Failed to fetch user profile: ${String(error)}`);
  }
});

const changeProfileImage = () => {
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = 'image/*';
  input.onchange = (e) => {
    const target = e.target as HTMLInputElement | null;
    const file = target?.files?.[0];
    if (file) {
      const reader = new FileReader();

      reader.readAsDataURL(file);
    }
  };
  input.click();
};

onMounted(() => {
  headerActionsStore.set(() =>
    h(
      'button',
      {
        class:
          'p-2 transition-colors hover:bg-gray-100 cursor-pointer rounded-md text-gray-800 font-semibold text-base',
      },
      t('profile.edit.save'),
    ),
  );
});

onUnmounted(() => {
  headerActionsStore.clear();
});
</script>

<template>
  <IonPage>
    <div class="scroll-container flex h-full flex-col items-center gap-y-4">
      <NForm label-placement="top" class="w-full space-y-6 p-4">
        <div class="rounded-lg bg-white p-4 shadow-sm">
          <div class="flex flex-col items-center">
            <div class="relative">
              <div class="flex h-24 w-24 items-center justify-center overflow-hidden rounded-full bg-gray-200">
                <img v-if="userStore.user?.profile_image" :src="userStore.user?.profile_image" alt="Profile"
                  class="h-full w-full object-cover" />
                <User v-else class="h-12 w-12 text-gray-400" />
              </div>
              <Button variant="custom" size="icon" @click="changeProfileImage"
                class="absolute right-0 bottom-0 rounded-full bg-gray-500">
                <Camera class="size-4" />
              </Button>
            </div>
            <p class="mt-2 text-sm text-gray-500">
              {{ t('profile.edit.tap_to_change_photo') }}
            </p>
          </div>
        </div>

        <!-- Personal Information -->
        <div class="rounded-lg bg-white p-4 shadow-sm">
          <h2 class="mb-4 flex items-center text-lg font-medium">
            <User class="mr-2 h-5 w-5 text-gray-600" />
            {{ t('profile.edit.personal_information') }}
          </h2>
          <NFormItem :label="t('profile.full_name')">
            <NInput :placeholder="t('profile.full_name')" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.birthday')">
            <NDatePicker class="w-full" :format="dateFormats.date.display" :placeholder="dateFormats.date.display" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.birth_place')">
            <NInput placeholder="" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.gender')">
            <NSelect :options="genderOptions" :placeholder="t('profile.edit.select_gender')" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.home_town')">
            <NInput placeholder="" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.current_address')">
            <NInput type="textarea" placeholder="" class="h-[80px]" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.religion')">
            <NInput placeholder="" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.nation')">
            <NInput :placeholder="t('profile.edit.select_nation')" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.education_level')">
            <NSelect :options="educationOptions" :placeholder="t('profile.edit.select_education_level')" />
          </NFormItem>
        </div>

        <!-- Contact Information -->
        <div class="rounded-lg bg-white p-4 shadow-sm">
          <h2 class="mb-4 flex items-center text-lg font-medium">
            <Phone class="mr-2 h-5 w-5 text-gray-600" />
            {{ t('profile.edit.contact_information') }}
          </h2>

          <NFormItem :label="t('profile.email')" class="mb-3">
            <NAutoComplete :placeholder="t('profile.edit.email_placeholder')" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.phone_number')">
            <NInput :placeholder="t('profile.edit.phone_number_placeholder')" />
          </NFormItem>
        </div>

        <!-- Work Information -->
        <div class="rounded-lg bg-white p-4 shadow-sm">
          <h2 class="mb-4 flex items-center text-lg font-medium">
            <Briefcase class="mr-2 h-5 w-5 text-gray-600" />
            {{ t('profile.edit.work_information') }}
          </h2>

          <NFormItem :label="t('profile.edit.staff_id')" class="mb-3">
            <NInput placeholder="" disabled />
            <template #feedback>
              <p class="mt-1 text-xs text-gray-500">
                {{ t('profile.edit.staff_id_feedback') }}
              </p>
            </template>
          </NFormItem>

          <NFormItem :label="t('profile.edit.position')">
            <NInput placeholder="" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.department')">
            <NInput placeholder="" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.workplace')">
            <NInput placeholder="" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.workplace_address')">
            <NInput type="textarea" placeholder="" class="h-[80px]" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.start_work_date')">
            <NDatePicker placeholder="dd/MM/yyy" class="w-full" />
          </NFormItem>
        </div>

        <!-- Identity Information -->
        <div class="rounded-lg bg-white p-4 shadow-sm">
          <h2 class="mb-4 flex items-center text-lg font-medium">
            <CreditCard class="mr-2 h-5 w-5 text-gray-600" />
            {{ t('profile.edit.identity_information') }}
          </h2>

          <NFormItem :label="t('profile.edit.identification_number')" class="mb-3">
            <NInput placeholder="" disabled />
            <template #feedback>
              <p class="mt-1 text-xs text-gray-500">
                {{ t('profile.edit.identification_number_feedback') }}
              </p>
            </template>
          </NFormItem>

          <NFormItem :label="t('profile.edit.place_of_issue')">
            <NInput placeholder="" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.issue_date')">
            <NDatePicker placeholder="dd/MM/yyyy" class="w-full" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.personal_tax_code')">
            <NInput placeholder="" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.social_insurance_number')">
            <NInput placeholder="" />
          </NFormItem>
        </div>

        <!-- Banking Information -->
        <div class="rounded-lg bg-white p-4 shadow-sm">
          <h2 class="mb-4 flex items-center text-lg font-medium">
            <CreditCard class="mr-2 h-5 w-5 text-gray-600" />
            {{ t('profile.edit.banking_information') }}
          </h2>

          <NFormItem :label="t('profile.edit.account_number')">
            <NInput placeholder="" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.account_name')">
            <NInput placeholder="" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.bank_name')">
            <NInput placeholder="" />
          </NFormItem>
        </div>

        <Button size="lg" type="submit" class="mb-4 w-full">
          {{ t('profile.edit.save_changes') }}
        </Button>
      </NForm>
    </div>
  </IonPage>
</template>
