import type { ApproveLeaveRequestPayload, ApproveLeaveRequestResponse, LeaveBalance, LeaveBalanceResponse, LeaveDaysOffResponse, LeaveItemResponse, LeaveMutationResponse, LeaveRequest, LeaveResponse, LeaveStatistics, LeaveStatisticsResponse, ManagementLeaveResponse, StaffRemain } from '@/interfaces/leave';
import type { BulkUpdateRequestsPayload, BulkUpdateRequestsResponse } from '@/interfaces/attendance';

import { PAGE_SIZE } from '@/constants';
import apiClient from '@/api/axios-instance';

export const getLeaveRequests = async (): Promise<LeaveResponse> => {
  const { data } = await apiClient.get<LeaveResponse>(
    '/timesheet/leave-requests',
  );

  return data;
};

export const getLeaveRequest = async (
  id: number,
): Promise<LeaveItemResponse> => {
  const { data } = await apiClient.get<LeaveItemResponse>(
    `/timesheet/leave-requests/id/${id}`,
  );

  return data;
};

export const createLeaveRequest = async (
  payload: LeaveRequest,
): Promise<LeaveMutationResponse> => {
  const { data } = await apiClient.post('/timesheet/leave-requests', payload);

  return data;
};

export const updateLeaveRequest = async (
  id: number,
  payload: Partial<LeaveRequest>,
): Promise<LeaveMutationResponse> => {
  const { data } = await apiClient.put(
    `/timesheet/leave-requests/id/${id}`,
    payload,
  );

  return data;
};

export const getStaffWithRemain = async (): Promise<StaffRemain[]> => {
  const { data } = await apiClient.get<LeaveDaysOffResponse>(
    '/timesheet/staff-with-remain',
  );

  return data.data ?? [];
};

export const getLeaveBalance = async (): Promise<LeaveBalance[]> => {
  const { data } = await apiClient.get<LeaveBalanceResponse>(
    '/timesheet/leave-balances',
  );

  return data.data ?? [];
};

export const bulkUpdateRequests = async (
  payload: BulkUpdateRequestsPayload,
): Promise<BulkUpdateRequestsResponse> => {
  const { data } = await apiClient.put<BulkUpdateRequestsResponse>(
    '/timesheet/bulk-update-requests',
    payload,
  );

  return data;
};

export const getRecentRequests = async (
  limit: number = PAGE_SIZE,
): Promise<LeaveResponse> => {
  const { data } = await apiClient.get<LeaveResponse>(
    '/timesheet/recent-requests',
    {
      params: { limit },
    },
  );

  return data;
};

export const getManagementLeaveRequests = async (
  filters?: Record<string, any>,
): Promise<ManagementLeaveResponse> => {
  const { data } = await apiClient.get<ManagementLeaveResponse>(
    '/timesheet/leave-requests/management',
    {
      params: filters,
    },
  );

  return data;
};

export const approveLeaveRequest = async (
  payload: ApproveLeaveRequestPayload,
): Promise<ApproveLeaveRequestResponse> => {
  const { data } = await apiClient.put<ApproveLeaveRequestResponse>(
    '/timesheet/leave-requests/approve',
    payload,
  );

  return data;
};

export const getStatistics = async (): Promise<LeaveStatistics> => {
  const { data } = await apiClient.get<LeaveStatisticsResponse>(
    '/leave/statistics',
  );

  return data.data;
};
