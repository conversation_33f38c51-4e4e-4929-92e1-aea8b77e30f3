<template>
  <div class="rounded-lg border border-gray-200 bg-white p-4 shadow-sm">
    <!-- Header with ID and Status -->
    <div class="mb-3 flex items-center justify-between">
      <Skeleton class="h-5 w-24" />
      <Skeleton class="h-6 w-20 rounded-full" />
    </div>

    <!-- Main Info Grid -->
    <div class="mb-3 grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
      <div class="flex items-center justify-between">
        <Skeleton class="h-4 w-10" />
        <Skeleton class="h-4 w-20" />
      </div>

      <div class="flex items-center justify-between">
        <Skeleton class="h-4 w-16" />
        <Skeleton class="h-4 w-12" />
      </div>

      <div class="col-span-2 flex items-center justify-between">
        <Skeleton class="h-4 w-8" />
        <Skeleton class="h-4 w-24" />
      </div>
    </div>

    <!-- Creator Info -->
    <div class="mb-2 text-sm">
      <div class="flex items-center justify-between">
        <Skeleton class="h-4 w-12" />
        <div class="text-right">
          <Skeleton class="h-4 w-32" />
        </div>
      </div>
    </div>

    <!-- Approver Info -->
    <div class="mb-3 text-sm">
      <div class="flex items-center justify-between">
        <Skeleton class="h-4 w-16" />
        <Skeleton class="h-4 w-28" />
      </div>
    </div>

    <!-- Reason -->
    <div class="rounded-md bg-gray-50 p-2">
      <Skeleton class="mb-2 h-3 w-12" />
      <Skeleton class="mb-1 h-4 w-full" />
      <Skeleton class="h-4 w-3/4" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { Skeleton } from '@/components/ui/skeleton';
</script>
