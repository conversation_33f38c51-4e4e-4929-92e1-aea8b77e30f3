<!-- eslint-disable no-unused-vars -->
<script setup lang="ts">
import { useLocale } from '@/composables/useLocale';
import type { AttendanceType } from '@/enums/attendance';
import type { Attendance } from '@/interfaces/attendance';
import { formatDate } from '@/utils/format';
import { Calendar, Clock, Timer } from 'lucide-vue-next';
import { useI18n } from 'vue-i18n';

defineProps<{
  filteredRecords: Attendance[];
  getDayOfWeek: (date: string) => string;
  getTypeClass: (type: AttendanceType) => string;
  getTypeText: (type: AttendanceType) => string;
  loading?: boolean;
}>();

const { locale } = useLocale();
const { t } = useI18n();
</script>

<template>
  <div class="mt-4 w-full space-y-3">
    <div v-for="record in filteredRecords" :key="record.id"
      class="rounded-xl border border-gray-200 bg-white p-4 shadow-sm">
      <div class="mb-3 flex items-center justify-between">
        <div class="flex items-center space-x-3">
          <div class="flex h-10 w-10 items-center justify-center rounded-full bg-gray-100">
            <Calendar class="h-5 w-5 text-gray-600" />
          </div>
          <div>
            <div class="font-medium text-gray-900">
              {{ formatDate(record.date, locale) }}
            </div>
            <div class="text-sm text-gray-500">
              {{ t(getDayOfWeek(record.date)) }}
            </div>
          </div>
        </div>
        <!-- <div class="flex items-center">
          <span
            :class="getTypeClass(record.type)"
            class="rounded-full px-2 py-1 text-xs font-medium"
          >
            {{ t(getTypeText(record.type)) }}
          </span>
        </div> -->
      </div>

      <div class="grid grid-cols-2 gap-4">
        <div class="flex items-center space-x-2">
          <Clock class="h-4 w-4 text-emerald-600" />
          <div>
            <div class="text-xs text-gray-500">
              {{ t('attendance.details.time_in') }}
            </div>
            <div class="font-medium text-gray-900">
              {{ record.time_in || '--:--' }}
            </div>
          </div>
        </div>
        <div class="flex items-center space-x-2">
          <Clock class="h-4 w-4 text-red-600" />
          <div>
            <div class="text-xs text-gray-500">
              {{ t('attendance.details.time_out') }}
            </div>
            <div class="font-medium text-gray-900">
              {{ record.time_out || '--:--' }}
            </div>
          </div>
        </div>
      </div>

      <div class="mt-3 border-t border-gray-100 pt-3">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <Timer class="h-4 w-4 text-gray-600" />
            <span class="text-sm text-gray-600">
              {{ t('attendance.details.total_time') }}:
            </span>
          </div>
          <span class="font-medium text-gray-900">
            {{ record.total_time || '0h 0m' }}
          </span>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-if="filteredRecords.length === 0" class="py-12 text-center">
      <div class="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-gray-100">
        <Calendar class="h-8 w-8 text-gray-400" />
      </div>
      <h3 class="mb-2 text-lg font-medium text-gray-900">
        {{ t('attendance.message.no_data') }}
      </h3>
      <p class="text-gray-500">
        {{ t('attendance.message.no_attendance_history') }}
      </p>
    </div>
  </div>
</template>
