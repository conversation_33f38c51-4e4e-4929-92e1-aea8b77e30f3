import { DATE_FORMATS, DATE_PLACEHOLDERS } from '@/constants/data-formats';

export const useDateFormats = () => {
  const dateFormats = {
    // Date formats with different display styles
    date: {
      display: DATE_FORMATS.DISPLAY_DATE_MDY, // dd/MM/yyyy
      displayDMY: DATE_FORMATS.DISPLAY_DATE_DMY, // dd-MM-yyyy
      value: DATE_FORMATS.VALUE_DATE, // yyyy-MM-dd
      placeholder: DATE_PLACEHOLDERS.DATE,
      placeholderDMY: DATE_PLACEHOLDERS.DATE_DMY,
      api: DATE_FORMATS.API_DATE,
    },
    datetime: {
      display: DATE_FORMATS.DISPLAY_DATETIME, // dd/MM/yyyy HH:mm
      displayWithSec: DATE_FORMATS.DISPLAY_DATETIME_SEC, // dd/MM/yyyy HH:mm:ss
      value: DATE_FORMATS.VALUE_DATETIME, // yyyy-MM-dd HH:mm:ss
      placeholder: DATE_PLACEHOLDERS.DATETIME,
      api: DATE_FORMATS.API_DATETIME,
    },
    time: {
      display: DATE_FORMATS.DISPLAY_TIME, // HH:mm
      displayWithSec: DATE_FORMATS.DISPLAY_TIME_SEC, // HH:mm:ss
      display12H: DATE_FORMATS.DISPLAY_TIME_12H, // hh:mm a
      value: DATE_FORMATS.VALUE_TIME, // HH:mm:ss
      placeholder: DATE_PLACEHOLDERS.TIME,
      placeholderWithSec: DATE_PLACEHOLDERS.TIME_SEC,
      placeholder12H: DATE_PLACEHOLDERS.TIME_12H,
      api: DATE_FORMATS.API_TIME,
    },
  };

  // Pre-configured picker configs for common use cases
  const pickerConfigs = {
    // For attendance forms (dd-MM-yyyy display, yyyy-MM-dd value)
    attendance: {
      date: {
        format: dateFormats.date.displayDMY,
        valueFormat: dateFormats.date.value,
        placeholder: dateFormats.date.placeholderDMY,
      },
      time: {
        format: dateFormats.time.display,
        placeholder: dateFormats.time.placeholder,
      },
      datetime: {
        format: dateFormats.datetime.display,
        valueFormat: dateFormats.datetime.value,
        placeholder: dateFormats.datetime.placeholder,
      },
    },
    // For leave forms (dd/MM/yyyy display)
    leave: {
      date: {
        format: dateFormats.date.display,
        valueFormat: dateFormats.date.value,
        placeholder: dateFormats.date.placeholder,
      },
      datetime: {
        format: dateFormats.datetime.display,
        valueFormat: dateFormats.datetime.value,
        placeholder: dateFormats.datetime.placeholder,
      },
    },
    // For general use
    general: {
      date: {
        format: dateFormats.date.display,
        valueFormat: dateFormats.date.value,
        placeholder: dateFormats.date.placeholder,
      },
      time: {
        format: dateFormats.time.display,
        placeholder: dateFormats.time.placeholder,
      },
      timeWithSec: {
        format: dateFormats.time.displayWithSec,
        placeholder: dateFormats.time.placeholderWithSec,
      },
      time12H: {
        format: dateFormats.time.display12H,
        placeholder: dateFormats.time.placeholder12H,
      },
      datetime: {
        format: dateFormats.datetime.display,
        valueFormat: dateFormats.datetime.value,
        placeholder: dateFormats.datetime.placeholder,
      },
    },
  };

  // Utility functions
  const formatForDisplay = (
    date: Date | string | number,
    type: 'date' | 'datetime' | 'time' = 'date',
  ) => {
    if (!date) return '';
    const d = new Date(date);

    switch (type) {
      case 'date':
        return d.toLocaleDateString('vi-VN');
      case 'datetime':
        return d.toLocaleString('vi-VN');
      case 'time':
        return d.toLocaleTimeString('vi-VN', {
          hour: '2-digit',
          minute: '2-digit',
        });
      default:
        return d.toLocaleDateString('vi-VN');
    }
  };

  const formatForAPI = (
    date: Date | string | number,
    type: 'date' | 'datetime' | 'time' = 'date',
  ) => {
    if (!date) return '';
    const d = new Date(date);

    switch (type) {
      case 'date':
        return d.toISOString().split('T')[0];
      case 'datetime':
        return d.toISOString().slice(0, 19).replace('T', ' ');
      case 'time':
        return d.toTimeString().slice(0, 8);
      default:
        return d.toISOString().split('T')[0];
    }
  };

  return {
    dateFormats,
    pickerConfigs,
    formatForDisplay,
    formatForAPI,
  };
};
