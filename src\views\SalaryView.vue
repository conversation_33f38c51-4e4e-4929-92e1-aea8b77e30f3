<script setup lang="ts">
import { IonPage } from '@ionic/vue'
import { NSelect } from 'naive-ui'
import { computed, ref } from 'vue'

const selectedMonth = ref<number | null>(-1)

const salarySlips = ref([
  {
    id: 1,
    month: 'December',
    year: 2023,
    basicSalary: 5000,
    allowances: 500,
    deductions: 300,
    netSalary: 5200,
    paymentDate: '2023-12-05',
    status: 'Paid',
  },
  {
    id: 2,
    month: 'November',
    year: 2023,
    basicSalary: 5000,
    allowances: 500,
    deductions: 300,
    netSalary: 5200,
    paymentDate: '2023-11-05',
    status: 'Paid',
  },
  {
    id: 3,
    month: 'October',
    year: 2023,
    basicSalary: 5000,
    allowances: 450,
    deductions: 300,
    netSalary: 5150,
    paymentDate: '2023-10-05',
    status: 'Paid',
  },
])

const monthOptions = ref([
  { label: 'All Months', value: -1 },
  { label: 'January', value: 1 },
  { label: 'February', value: 2 },
  { label: 'March', value: 3 },
  { label: 'April', value: 4 },
  { label: 'May', value: 5 },
  { label: 'June', value: 6 },
  { label: 'July', value: 7 },
  { label: 'August', value: 8 },
  { label: 'September', value: 9 },
  { label: 'October', value: 10 },
  { label: 'November', value: 11 },
  { label: 'December', value: 12 },
])

const filteredSalarySlips = computed(() => {
  if (selectedMonth.value === -1) {
    return salarySlips.value
  }
  const monthNames = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
  ]
  return salarySlips.value.filter(
    (slip) => monthNames.indexOf(slip.month) + 1 === selectedMonth.value,
  )
})

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount)
}
</script>
<template>
  <ion-page class="ion-padding">
    <div class="flex flex-col items-center gap-y-7 h-full scroll-container mt-2 p-1.5">
      <div
        class="relative flex w-full flex-col gap-5 rounded-md bg-white px-3.5 py-5 shadow-shadow-1"
      >
        <h4 class="font-bold text-lg text-gray-800">Salary Slips</h4>
        <n-select
          v-model:value="selectedMonth"
          :options="monthOptions"
          placeholder="Filter by month"
          clearable
        />
      </div>

      <div v-if="filteredSalarySlips.length > 0" class="w-full flex flex-col gap-4">
        <div
          v-for="slip in filteredSalarySlips"
          :key="slip.id"
          class="flex flex-col gap-4 rounded-md bg-white px-3.5 py-5 shadow"
        >
          <h5 class="font-semibold text-gray-800">{{ slip.month }} {{ slip.year }} Salary Slip</h5>

          <div class="flex justify-between py-2 border-b border-gray-100">
            <span class="text-sm text-gray-600">Basic Salary</span>
            <span class="font-medium">{{ formatCurrency(slip.basicSalary) }}</span>
          </div>

          <div class="flex justify-between py-2 border-b border-gray-100">
            <span class="text-sm text-gray-600">Allowances</span>
            <span class="font-medium text-green-600">+{{ formatCurrency(slip.allowances) }}</span>
          </div>

          <div class="flex justify-between py-2 border-b border-gray-100">
            <span class="text-sm text-gray-600">Deductions</span>
            <span class="font-medium text-red-600">-{{ formatCurrency(slip.deductions) }}</span>
          </div>

          <div class="flex justify-between py-2 mt-2">
            <span class="text-base font-semibold">Net Salary</span>
            <span class="text-base font-bold">{{ formatCurrency(slip.netSalary) }}</span>
          </div>

          <div class="flex justify-between mt-4 text-xs text-gray-500">
            <span>Payment Date: {{ slip.paymentDate }}</span>
            <span class="px-2 py-1 rounded-full bg-green-100 text-green-800">{{
              slip.status
            }}</span>
          </div>
        </div>
      </div>

      <div v-else class="mt-5 mb-7 flex w-full flex-col items-center">
        <p class="flex flex-col items-center rounded p-5 text-sm text-gray-600">
          No salary slips found
        </p>
      </div>
    </div>
  </ion-page>
</template>
