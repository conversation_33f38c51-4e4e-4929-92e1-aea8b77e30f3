# 🐳 Docker Quick Start Guide

## 🚀 Quick Commands

### Start the application
```bash
docker-compose up --build -d
```

### Check status
```bash
docker-compose ps
curl http://localhost/health
```

### View logs
```bash
docker-compose logs -f sharp-app
```

### Stop the application
```bash
docker-compose down
```

### Restart after nginx config changes
```bash
docker-compose restart sharp-app
```

## 🔧 Nginx Configuration

### Main config files:
- `nginx/nginx.conf` - Main nginx configuration
- `nginx/default.conf` - Server configuration with PWA support

### To modify nginx:
1. Edit files in `nginx/` directory
2. Restart container: `docker-compose restart sharp-app`
3. Test config: `docker-compose exec sharp-app nginx -t`

## 🌐 Access Points

- **Application**: http://localhost
- **Health Check**: http://localhost/health
- **PWA Manifest**: http://localhost/manifest.webmanifest

## 🐛 Troubleshooting

### View container logs:
```bash
docker-compose logs sharp-app
```

### Access container shell:
```bash
docker-compose exec sharp-app sh
```

### Clean rebuild:
```bash
docker-compose down
docker system prune -f
docker-compose up --build -d
```

## 📝 Notes

- Firebase configuration is embedded during build time
- No Husky hooks run in Docker (as intended)
- PWA features fully supported
- Nginx optimized for Vue.js SPA routing
