<script setup lang="ts">
import RejectModal from '@/components/management/RejectModal.vue';
import { useLeaveRequests } from '@/composables/useLeaveRequests';
import { Status } from '@/enums';
import { computed, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { toast } from 'vue-sonner';
import { Skeleton } from '../ui/skeleton';
import { Button } from '../ui/button';
import { useLeaveForm } from '@/composables/useLeaveForm';
import { NSelect, NDatePicker, } from 'naive-ui'
import { Label } from '../ui/label';
import type { GroupLeaveRequest, IndividualLeaveRequest, LeaveEmployeeInfo } from '@/interfaces/leave';
import type { BulkUpdateRequestsPayload } from '@/interfaces/attendance';
import { useDateFormats } from '@/composables/useDateFormats';
import { Action } from '@/enums/leave';

// Use union type of existing interfaces from data.ts
type LeaveRequestDisplay = {
  id: string | number;
  type: string;
  subType: 'group' | 'individual';
  employeeName: string;
  startDate: string;
  endDate: string;
  status: number;
  details: string;
  rejectionReason?: string;
  canApprove: boolean;
  canReject: boolean;
  leaveDays: number;
  leaveType: string;
  groupCode?: string;
  employees?: LeaveEmployeeInfo[];
  originalData: GroupLeaveRequest | IndividualLeaveRequest;
};


// interface Props {
//   statusFilter: Status | 'all';
// }
// const props = defineProps<Props>();
const { dateFormats } = useDateFormats();

defineEmits<{
  'update:statusFilter': [value: Status | 'all'];
}>();

const { managementLeaveQuery, approveLeaveRequestMutation } =
  useLeaveRequests();
const { t } = useI18n();

// Leave filters
const leaveFilters = ref({
  page: 1,
  limit: 20,
});
const { leaveTypeOptions, staffApproveOptions } = useLeaveForm()

const statusOptions = [
  { label: t('leaves.status.ALL'), value: "all" },
  { label: t('leaves.status.PENDING'), value: Status.PENDING },
  { label: t('leaves.status.APPROVED'), value: Status.APPROVED },
  { label: t('leaves.status.REJECTED'), value: Status.REJECTED },
];

//mapping with Leave Type Options
const getLeaveTypeLabel = (value: number): string => {
  const found = leaveTypeOptions.value.find((opt) => opt.value === value);
  return found?.label || 'Unknown Leave Type';
};


// Leave requests query
const leaveRequestsQuery = managementLeaveQuery(leaveFilters);

// Transform leave data for display
const requests = computed((): LeaveRequestDisplay[] => {
  if (!leaveRequestsQuery.data.value?.data) return [];

  return leaveRequestsQuery.data.value.data.map((request: GroupLeaveRequest | IndividualLeaveRequest) => {
    // Handle both group and individual requests
    if (request.registration_type === 'group') {
      // Group request
      const groupRequest = request as GroupLeaveRequest;
      return {
        id: groupRequest.group_code,
        type: groupRequest.type,
        subType: 'group' as const,
        employeeName: groupRequest.creator_info.full_name,
        startDate: groupRequest.start_time,
        endDate: groupRequest.end_time,
        status: groupRequest.status,
        details: `${t('management.requests.leave.group_request')}: ${groupRequest.subject} (${groupRequest.total_employees_including_creator} ${t('management.requests.leave.employees')})`,
        rejectionReason: groupRequest.reject_reason || '',
        canApprove: groupRequest.can_approve,
        canReject: groupRequest.can_reject,
        leaveDays: groupRequest.number_of_leaving_day,
        leaveType: getLeaveTypeLabel(groupRequest.type_of_leave),
        groupCode: groupRequest.group_code,
        employees: [
          ...groupRequest.employees,
          ...(groupRequest.creator_employee
            ? [groupRequest.creator_employee]
            : []),
        ],
        originalData: groupRequest,
      };
    } else {
      // Individual request
      const individualRequest = request as IndividualLeaveRequest;
      return {
        id: individualRequest.id,
        type: individualRequest.type,
        subType: 'individual' as const,
        employeeName: individualRequest.employee_info.full_name,
        startDate: individualRequest.start_time,
        endDate: individualRequest.end_time,
        status: individualRequest.status,
        details: `${t('management.requests.leave.individual_request')}: ${individualRequest.subject}`,
        rejectionReason: individualRequest.reject_reason || '',
        canApprove: individualRequest.can_approve,
        canReject: individualRequest.can_reject,
        leaveDays: individualRequest.number_of_leaving_day,
        leaveType: getLeaveTypeLabel(individualRequest.type_of_leave),
        originalData: individualRequest,
      };
    }
  });
});
const filterState = ref({
  leaveType: null as number | null,
  status: 'all' as Status | 'all',
  approver: null as number | null,
  date: null as number | null,
  dateRange: null as [number, number] | null,
  quickDate: null as string | null,
});

// Helper function to get date range based on quick filter
const getQuickDateRange = (quickFilter: string): [Date, Date] | null => {
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

  switch (quickFilter) {
    case 'today':
      return [today, today];
    case 'yesterday':
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      return [yesterday, yesterday];
    case 'this_week':
      const startOfWeek = new Date(today);
      startOfWeek.setDate(today.getDate() - today.getDay());
      const endOfWeek = new Date(startOfWeek);
      endOfWeek.setDate(startOfWeek.getDate() + 6);
      return [startOfWeek, endOfWeek];
    case 'last_week':
      const lastWeekStart = new Date(today);
      lastWeekStart.setDate(today.getDate() - today.getDay() - 7);
      const lastWeekEnd = new Date(lastWeekStart);
      lastWeekEnd.setDate(lastWeekStart.getDate() + 6);
      return [lastWeekStart, lastWeekEnd];
    case 'this_month':
      const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
      const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);
      return [startOfMonth, endOfMonth];
    case 'last_month':
      const lastMonthStart = new Date(today.getFullYear(), today.getMonth() - 1, 1);
      const lastMonthEnd = new Date(today.getFullYear(), today.getMonth(), 0);
      return [lastMonthStart, lastMonthEnd];
    default:
      return null;
  }
};

// Watch for filter conflicts and clear others when one is selected
watch(() => filterState.value.date, (newDate) => {
  if (newDate !== null) {
    filterState.value.dateRange = null;
    filterState.value.quickDate = null;
  }
});

watch(() => filterState.value.dateRange, (newRange) => {
  if (newRange !== null) {
    filterState.value.date = null;
    filterState.value.quickDate = null;
  }
});

watch(() => filterState.value.quickDate, (newQuick) => {
  if (newQuick !== null) {
    filterState.value.date = null;
    filterState.value.dateRange = null;
  }
});

const filteredRequests = computed(() => {
  return requests.value.filter((request) => {
    const matchesStatus =
      filterState.value.status === 'all' || request.status === filterState.value.status;

    const matchesLeaveType =
      filterState.value.leaveType === null || request.originalData.type_of_leave === filterState.value.leaveType;

    const matchesApprover =
      filterState.value.approver === null || request.originalData.approver_id === filterState.value.approver;

    // Enhanced date filtering
    let matchesDate = true;
    const requestDate = new Date(request.startDate).setHours(0, 0, 0, 0);

    // Single date filter
    if (filterState.value.date !== null) {
      matchesDate = requestDate === new Date(filterState.value.date).setHours(0, 0, 0, 0);
    }

    // Date range filter
    if (filterState.value.dateRange !== null) {
      const [startDate, endDate] = filterState.value.dateRange;
      const start = new Date(startDate).setHours(0, 0, 0, 0);
      const end = new Date(endDate).setHours(23, 59, 59, 999);
      matchesDate = requestDate >= start && requestDate <= end;
    }

    // Quick date filter
    if (filterState.value.quickDate !== null) {
      const quickRange = getQuickDateRange(filterState.value.quickDate);
      if (quickRange) {
        const [start, end] = quickRange;
        const startTime = start.setHours(0, 0, 0, 0);
        const endTime = end.setHours(23, 59, 59, 999);
        matchesDate = requestDate >= startTime && requestDate <= endTime;
      }
    }

    return matchesStatus && matchesLeaveType && matchesApprover && matchesDate;
  });
});

// Handle approve request
const handleApprove = async (request: LeaveRequestDisplay) => {
  try {
    const payload: BulkUpdateRequestsPayload = {
      action: Action.APPROVE,
    };

    if (request.subType === 'group') {
      // For group requests, use group_code
      payload.group_code = String(request.groupCode);
    } else {
      // For individual requests, use id
      payload.id = Number(request.id);
    }

    const response = await approveLeaveRequestMutation.mutateAsync(payload);

    if (response.success) {
      toast.success(
        response.message || t('management.requests.messages.approve_success'),
      );

      // Show additional info if available
      if (response.summary && response.summary.successful_count > 0) {
        const { successful_count, total_requests } = response.summary;
        if (successful_count < total_requests) {
          toast.warning(
            `${successful_count}/${total_requests} requests processed successfully`,
          );
        }
      }
    } else {
      toast.error(response.message || t('common.error.something_went_wrong'));
    }
  } catch (error: any) {
    console.error('Error approving request:', error);
    const errorMessage =
      error?.response?.data?.message ||
      error?.message ||
      t('common.error.something_went_wrong');
    toast.error(errorMessage);
  }
};

// Handle reject request
const handleReject = async (request: LeaveRequestDisplay, reason: string) => {
  try {
    const payload: BulkUpdateRequestsPayload = {
      action: Action.REJECT,
      reject_reason: reason,
    };

    if (request.subType === 'group') {
      // For group requests, use group_code
      payload.group_code = String(request.groupCode);
    } else {
      // For individual requests, use id
      payload.id = Number(request.id)
    }
    const response = await approveLeaveRequestMutation.mutateAsync(payload);

    if (response.success) {
      toast.success(
        response.message || t('management.requests.messages.reject_success'),
      );

      // Show additional info if available
      if (response.summary && response.summary.successful_count > 0) {
        const { successful_count, total_requests } = response.summary;
        if (successful_count < total_requests) {
          toast.warning(
            `${successful_count}/${total_requests} requests processed successfully`,
          );
        }
      }
    } else {
      toast.error(response.message || t('common.error.something_went_wrong'));
    }
  } catch (error: any) {
    console.error('Error rejecting request:', error);
    const errorMessage =
      error?.response?.data?.message ||
      error?.message ||
      t('common.error.something_went_wrong');
    toast.error(errorMessage);
  }
};

// Modal states for rejection
const showRejectModal = ref(false);
const selectedRequest = ref<LeaveRequestDisplay | null>(null);

const openRejectModal = (request: LeaveRequestDisplay) => {
  selectedRequest.value = request;
  showRejectModal.value = true;
};

const closeRejectModal = () => {
  showRejectModal.value = false;
  selectedRequest.value = null;
};

const confirmReject = (reason: string) => {
  if (selectedRequest.value) {
    handleReject(selectedRequest.value, reason);
    closeRejectModal();
  }
};

const formatDate = (dateString: string) => {
  if (!dateString) return '';
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
};

const getStatusText = (status: Status): string => {
  switch (status) {
    case Status.PENDING:
      return t('common.status.pending');
    case Status.APPROVED:
      return t('common.status.approved');
    case Status.REJECTED:
      return t('common.status.rejected');
    default:
      return '';
  }
};

const getStatusColor = (status: Status): string => {
  switch (status) {
    case Status.PENDING:
      return 'amber';
    case Status.APPROVED:
      return 'emerald';
    case Status.REJECTED:
      return 'red';
    default:
      return 'gray';
  }
};
</script>

<template>
  <div class="leave-requests-tab">
    <div class="space-y-4 mb-6">
      <!-- Row 1: Leave Type & Status -->
      <div class="grid grid-cols-2 md:grid-cols-2 gap-3">
        <div class="space-y-1.5">
          <Label class="font-medium text-sm text-muted-foreground">{{ t('leaves.filter.leave_type') }}</Label>
          <n-select v-model:value="filterState.leaveType" filterable :options="leaveTypeOptions"
            :placeholder="t('leaves.new_request.type_of_leave_placeholder')" clearable />
        </div>
        <div class="space-y-1.5">
          <Label class="font-medium text-sm text-muted-foreground">{{ t('leaves.filter.status') }}</Label>
          <n-select v-model:value="filterState.status" default-value="all" filterable :options="statusOptions"
            :placeholder="t('leaves.filter.all_status')" />
        </div>
      </div>

      <!-- Row 2: Approver & Quick Date Filter -->
      <div class="grid grid-cols-2 md:grid-cols-2 gap-3">
        <div class="space-y-1.5">
          <Label class="font-medium text-sm text-muted-foreground">{{ t('leaves.filter.approver') }}</Label>
          <n-select v-model:value="filterState.approver" filterable :options="staffApproveOptions"
            :placeholder="t('leaves.new_request.approver_placeholder')" clearable />
        </div>


        <div class="space-y-1.5">
          <Label class="font-medium text-sm text-muted-foreground">{{ t('leaves.filter.select_date') }}</Label>
          <n-date-picker v-model:value="filterState.dateRange" type="daterange" :format="dateFormats.date.display"
            :placeholder='t("leaves.requests.date_placeholder")' clearable class="w-full" />
        </div>
      </div>



      <!-- Clear Filters Button -->
      <div class="flex justify-end pt-2">
        <Button variant="outline" size="sm"
          @click="filterState = { leaveType: null, status: 'all', approver: null, date: null, dateRange: null, quickDate: null }">
          {{ t('leaves.common.clear_filter') }}
        </Button>
      </div>
    </div>

    <!-- Loading Skeleton -->
    <div v-if="leaveRequestsQuery.isLoading.value || approveLeaveRequestMutation.isPending.value" class="space-y-4">
      <div v-for="n in 4" :key="n" class="rounded-lg border bg-white p-4 shadow-sm space-y-3">
        <Skeleton class="h-5 w-1/2" />
        <Skeleton class="h-4 w-1/3" />
        <Skeleton class="h-4 w-1/4" />
        <Skeleton class="h-4 w-full" />
        <div class="flex gap-2">
          <Skeleton class="h-8 w-24 rounded" />
          <Skeleton class="h-8 w-24 rounded" />
        </div>
      </div>
    </div>

    <!-- Requests List -->
    <div v-else class="space-y-4">
      <div v-for="request in filteredRequests" :key="request.id"
        class="rounded-lg border bg-white p-4 shadow-sm hover:shadow-md">
        <div class="mb-3 flex items-start justify-between">
          <div>
            <h3 class="text-lg font-semibold">{{ request.employeeName }}</h3>
            <p class="text-xs text-gray-600">
              {{ formatDate(request.startDate) }} -
              {{ formatDate(request.endDate) }}
            </p>
            <div class="mt-1 flex items-center gap-2 text-xs text-gray-500">
              <span>{{ request.leaveType }}</span>
              <span>•</span>
              <span>{{ request.leaveDays }}
                {{ t('management.requests.leave.days') }}</span>
            </div>
          </div>
          <div class="flex gap-2">
            <span :class="[
              'rounded px-2 py-1 text-xs font-medium',
              `bg-${getStatusColor(request.status)}-100 text-${getStatusColor(request.status)}-600 border border-${getStatusColor(request.status)}-500`,
            ]">
              {{ getStatusText(request.status) }}
            </span>
            <span :class="[
              'rounded px-2 py-1 text-xs font-medium',
              request.subType === 'group'
                ? 'bg-sky-100 text-sky-700 border border-sky-500'
                : 'bg-purple-100 text-purple-700 border border-purple-500',
            ]">
              {{
                request.subType === 'group'
                  ? t('management.requests.leave.group')
                  : t('management.requests.leave.individual')
              }}
            </span>
          </div>
        </div>

        <p class="mb-4 text-gray-700">{{ request.details }}</p>

        <!-- Group Members (for group requests) -->
        <div v-if="request.subType === 'group' && request.employees" class="mb-4 rounded bg-gray-50 p-3">
          <h4 class="mb-2 text-sm font-medium text-gray-700">
            {{ t('management.requests.leave.group_members') }}:
          </h4>
          <div class="grid grid-cols-1 gap-2">
            <div v-for="employee in request.employees" :key="employee.id" class="flex items-center text-sm">
              <span class="font-medium">{{ employee.full_name }}</span>
              <span class="ml-2 text-gray-500">({{ employee.staff_identifi }})</span>
            </div>
          </div>
        </div>

        <div v-if="request.status === Status.REJECTED && request.rejectionReason"
          class="mb-4 rounded border-l-4 border-red-400 bg-red-50 p-3">
          <p class="text-sm text-red-700">
            <span class="font-medium">{{ t('management.requests.rejection_reason') }}:</span>
            {{ request.rejectionReason }}
          </p>
        </div>

        <!-- Action Buttons -->
        <div class="flex gap-2">
          <Button v-if="request.status === Status.PENDING && request.canApprove" @click="handleApprove(request)"
            :disabled="approveLeaveRequestMutation.isPending.value" variant="default"
            class="flex-1 rounded-sm cursor-pointer disabled:cursor-not-allowed disabled:opacity-50">
            {{ t('management.requests.actions.approve') }}
          </Button>

          <Button v-if="request.status === Status.PENDING && request.canReject" @click="openRejectModal(request)"
            :disabled="approveLeaveRequestMutation.isPending.value" variant="outline"
            class="flex-1 cursor-pointer rounded-sm disabled:cursor-not-allowed disabled:opacity-50 hover:text-red-500 hover:border-red-500 hover:bg-red-100 transition-colors duration-200">
            {{ t('management.requests.actions.reject') }}
          </Button>
        </div>
      </div>

      <!-- Empty State -->
      <div v-if="filteredRequests.length === 0" class="py-8 text-center text-gray-500">
        <p>{{ t('management.requests.no_leave_requests') }}</p>
      </div>
    </div>

    <!-- Rejection Modal -->
    <RejectModal v-model:show="showRejectModal" @confirm="confirmReject" @cancel="closeRejectModal" />
  </div>
</template>

<style scoped>
.leave-requests-tab {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 16px;
}
</style>
