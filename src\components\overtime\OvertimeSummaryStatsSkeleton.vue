<template>
  <div class="-mt-5 grid w-full grid-cols-2 gap-4 lg:grid-cols-4">
    <div class="rounded-lg bg-white p-4 shadow-sm">
      <Skeleton class="mb-2 h-4 w-20" />
      <Skeleton class="h-8 w-12" />
    </div>

    <div class="rounded-lg bg-white p-4 shadow-sm">
      <Skeleton class="mb-2 h-4 w-16" />
      <Skeleton class="h-8 w-8" />
    </div>

    <div class="rounded-lg bg-white p-4 shadow-sm">
      <Skeleton class="mb-2 h-4 w-14" />
      <Skeleton class="h-8 w-8" />
    </div>

    <div class="rounded-lg bg-white p-4 shadow-sm">
      <Skeleton class="mb-2 h-4 w-20" />
      <Skeleton class="h-8 w-16" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { Skeleton } from '@/components/ui/skeleton';
</script>
