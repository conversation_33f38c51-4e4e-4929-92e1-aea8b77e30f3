#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

echo "🚀 Running pre-push checks..."

# Run linting
echo "📝 Running lint..."
npm run lint

if [ $? -ne 0 ]; then
    echo "❌ <PERSON>t failed! Please fix the errors before committing."
    exit 1
fi

# Run build to ensure code compiles (without type-check)
echo "🏗️  Running build..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ Build failed! Please fix the errors before committing."
    exit 1
fi

echo "✅ Pre-push checks completed!"
