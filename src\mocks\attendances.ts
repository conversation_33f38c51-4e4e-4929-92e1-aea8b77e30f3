import type { Attendance } from '@/interfaces/attendance';
import { ref } from 'vue';

export const attendanceRecords = ref<Attendance[]>([
  // Tháng 1/2024
  {
    id: 1,
    date: '2024-01-31',
    time_in: '08:30',
    time_out: '17:45',
    total_time: '8h 15m',
  },
  {
    id: 2,
    date: '2024-01-30',
    time_in: '09:15',
    time_out: '18:30',
    total_time: '8h 15m',
  },
  {
    id: 3,
    date: '2024-01-29',
    time_in: '08:00',
    time_out: '17:30',
    total_time: '8h 30m',
  },
  {
    id: 4,
    date: '2024-01-28',
    time_in: '',
    time_out: '',
    total_time: '0h 0m',
  },
  {
    id: 5,
    date: '2024-01-27',
    time_in: '08:45',
    time_out: '17:15',
    total_time: '7h 30m',
  },
  {
    id: 6,
    date: '2024-01-26',
    time_in: '08:30',
    time_out: '19:00',
    total_time: '9h 30m',
  },
  {
    id: 7,
    date: '2024-01-25',
    time_in: '08:15',
    time_out: '17:45',
    total_time: '8h 30m',
  },
  {
    id: 8,
    date: '2024-01-24',
    time_in: '09:30',
    time_out: '18:15',
    total_time: '7h 45m',
  },
  {
    id: 9,
    date: '2024-01-23',
    time_in: '08:30',
    time_out: '17:30',
    total_time: '8h 0m',
  },
  {
    id: 10,
    date: '2024-01-22',
    time_in: '08:45',
    time_out: '17:45',
    total_time: '8h 0m',
  },
  {
    id: 11,
    date: '2024-01-21',
    time_in: '',
    time_out: '',
    total_time: '0h 0m',
  },
  {
    id: 12,
    date: '2024-01-20',
    time_in: '08:00',
    time_out: '16:30',
    total_time: '7h 30m',
  },
  {
    id: 13,
    date: '2024-01-19',
    time_in: '08:30',
    time_out: '17:30',
    total_time: '8h 0m',
  },
  {
    id: 14,
    date: '2024-01-18',
    time_in: '09:00',
    time_out: '18:00',
    total_time: '8h 0m',
  },
  {
    id: 15,
    date: '2024-01-17',
    time_in: '08:15',
    time_out: '17:15',
    total_time: '8h 0m',
  },
  {
    id: 16,
    date: '2024-01-16',
    time_in: '08:30',
    time_out: '17:45',
    total_time: '8h 15m',
  },
  {
    id: 17,
    date: '2024-01-15',
    time_in: '08:45',
    time_out: '17:30',
    total_time: '7h 45m',
  },
  {
    id: 18,
    date: '2024-01-14',
    time_in: '',
    time_out: '',
    total_time: '0h 0m',
  },
  {
    id: 19,
    date: '2024-01-13',
    time_in: '08:00',
    time_out: '16:00',
    total_time: '7h 0m',
  },
  {
    id: 20,
    date: '2024-01-12',
    time_in: '08:30',
    time_out: '17:30',
    total_time: '8h 0m',
  },
  // Tháng 12/2023
  {
    id: 21,
    date: '2023-12-29',
    time_in: '08:30',
    time_out: '17:30',
    total_time: '8h 0m',
  },
  {
    id: 22,
    date: '2023-12-28',
    time_in: '09:15',
    time_out: '18:45',
    total_time: '8h 30m',
  },
  {
    id: 23,
    date: '2023-12-27',
    time_in: '08:00',
    time_out: '17:00',
    total_time: '8h 0m',
  },
  {
    id: 24,
    date: '2023-12-26',
    time_in: '08:30',
    time_out: '17:30',
    total_time: '8h 0m',
  },
  {
    id: 25,
    date: '2023-12-25',
    time_in: '',
    time_out: '',
    total_time: '0h 0m',
  },
  {
    id: 26,
    date: '2023-12-24',
    time_in: '08:00',
    time_out: '15:00',
    total_time: '6h 0m',
  },
  {
    id: 27,
    date: '2023-12-23',
    time_in: '08:30',
    time_out: '17:30',
    total_time: '8h 0m',
  },
  {
    id: 28,
    date: '2023-12-22',
    time_in: '08:45',
    time_out: '17:45',
    total_time: '8h 0m',
  },
  {
    id: 29,
    date: '2023-12-21',
    time_in: '09:30',
    time_out: '18:30',
    total_time: '8h 0m',
  },
  {
    id: 30,
    date: '2023-12-20',
    time_in: '08:15',
    time_out: '17:15',
    total_time: '8h 0m',
  },
  {
    id: 31,
    date: '2023-12-19',
    time_in: '08:30',
    time_out: '17:30',
    total_time: '8h 0m',
  },
  {
    id: 32,
    date: '2023-12-18',
    time_in: '08:00',
    time_out: '18:00',
    total_time: '9h 0m',
  },
  {
    id: 33,
    date: '2023-12-17',
    time_in: '',
    time_out: '',
    total_time: '0h 0m',
  },
  {
    id: 34,
    date: '2023-12-16',
    time_in: '08:30',
    time_out: '17:30',
    total_time: '8h 0m',
  },
  {
    id: 35,
    date: '2023-12-15',
    time_in: '09:00',
    time_out: '18:15',
    total_time: '8h 15m',
  },
  {
    id: 36,
    date: '2023-12-14',
    time_in: '08:15',
    time_out: '17:45',
    total_time: '8h 30m',
  },
  {
    id: 37,
    date: '2023-12-13',
    time_in: '08:30',
    time_out: '17:30',
    total_time: '8h 0m',
  },
  {
    id: 38,
    date: '2023-12-12',
    time_in: '08:45',
    time_out: '17:15',
    total_time: '7h 30m',
  },
  {
    id: 39,
    date: '2023-12-11',
    time_in: '08:00',
    time_out: '19:30',
    total_time: '10h 30m',
  },
  {
    id: 40,
    date: '2023-12-10',
    time_in: '',
    time_out: '',
    total_time: '0h 0m',
  },
  {
    id: 41,
    date: '2023-12-09',
    time_in: '08:30',
    time_out: '17:30',
    total_time: '8h 0m',
  },
  {
    id: 42,
    date: '2023-12-08',
    time_in: '09:15',
    time_out: '18:00',
    total_time: '7h 45m',
  },
  {
    id: 43,
    date: '2023-12-07',
    time_in: '08:00',
    time_out: '17:00',
    total_time: '8h 0m',
  },
  {
    id: 44,
    date: '2023-12-06',
    time_in: '08:30',
    time_out: '17:30',
    total_time: '8h 0m',
  },
  {
    id: 45,
    date: '2023-12-05',
    time_in: '08:45',
    time_out: '17:45',
    total_time: '8h 0m',
  },
]);
