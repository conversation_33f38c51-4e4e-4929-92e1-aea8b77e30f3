import { getMessaging, getToken, onMessage } from 'firebase/messaging';

import firebaseConfig from '@/configs/firebase-config';
import i18n from '@/plugins/i18n';
import { initializeApp } from 'firebase/app';
import VueCookies from 'vue-cookies';
import { toast } from 'vue-sonner';

initializeApp(firebaseConfig);
const cookies = VueCookies;

const messaging = getMessaging();

export function setupFirebaseMessaging() {
  onMessage(messaging, (payload) => {
    toast.info(
      i18n.global.t('common.firebase.message_received', {
        message: payload,
      }),
    );
  });

  getToken(messaging, { vapidKey: import.meta.env.VITE_FIREBASE_VAPID_KEY })
    .then((currentToken) => {
      if (currentToken) {
        cookies.set('fcm-token', currentToken, '30d');
      } else {
        toast.error(i18n.global.t('common.firebase.no_token'));
      }
    })
    .catch((err) => {
      toast.error(
        i18n.global.t('common.firebase.failed_to_get_token', {
          error: err.message,
        }),
      );
    });
}
