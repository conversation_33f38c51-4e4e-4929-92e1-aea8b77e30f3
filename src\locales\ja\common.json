{"common": {"nav": {"home": "ホーム", "about": "概要", "attendance": "出勤", "leaves": "休暇", "expenses": "経費", "salary": "給与", "profile": "プロフィール"}, "loading": "読み込み中...", "error": "エラーが発生しました", "save": "保存", "cancel": "キャンセル", "delete": "削除", "edit": "編集", "dismiss": "閉じる", "view": "表示", "submit": "送信", "back": "戻る", "next": "次へ", "confirm": "確認", "search": "検索", "filter": "フィルター", "sort": "並べ替え", "logout": "ログアウト", "choose_file": "ファイルを選択", "no_file_choose": "ファイルが選択されていません", "validation": {"required": "この項目は必須です", "email": "有効なメールアドレスを入力してください", "minLength": "この項目は少なくとも{min}文字必要です", "maxLength": "この項目は{max}文字未満である必要があります"}, "not_found": {"title": "ページが見つかりません", "message": "お探しのページは存在しないか、移動されました。", "go_home": "ホームに戻る"}, "sharp": "<PERSON>", "pagination": {"total": "合計: {total} アイテム", "page": "ページ {page} / {totalPages}"}, "alert": {"error": "エラー", "error_message": "リクエストの処理中にエラーが発生しました！", "success": "成功", "success_message": "リクエストが正常に処理されました！", "warning": "警告", "warning_message": "入力を確認して、もう一度やり直してください。", "info": "情報", "info_message": "これは情報メッセージです。"}, "toast": {"success": {"create": "{type} リクエストが正常に作成されました！", "update": "{type} リクエストが正常に更新されました！", "delete": "{type} リクエストが正常に削除されました！"}, "error": {"create": "{type} リクエストの作成中にエラーが発生しました！", "update": "{type} リクエストの更新中にエラーが発生しました！", "delete": "{type} リクエストの削除中にエラーが発生しました！", "set_locale": "言語の設定に失敗しました", "load_user": "ユーザー情報の取得に失敗しました", "unknown": "不明なエラーが発生しました", "something_went_wrong": "問題が発生しました"}}, "firebase": {"regiter_fail": "Service Workerの登録に失敗しました: {error}", "unsupported": "このブラウザはService Workerをサポートしていません", "failed_to_get_token": "トークンの取得に失敗しました: {error}", "message_received": "メッセージを受信しました: {message}", "no_token": "登録トークンは利用できません"}, "module_type": {"overtime": "残業", "leave": "休暇", "expense": "経費", "salary": "給与", "profile": "プロフィール"}, "weekdays": {"sunday": "日曜日", "monday": "月曜日", "tuesday": "火曜日", "wednesday": "水曜日", "thursday": "木曜日", "friday": "金曜日", "saturday": "土曜日"}, "status": {"all": "すべて", "pending": "保留中", "approved": "承認済み", "rejected": "却下済み"}}}