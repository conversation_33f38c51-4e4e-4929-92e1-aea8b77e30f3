# SSL configuration template
# Copy this file to ssl.conf and uncomment/modify as needed

# SSL configuration for HTTPS
# server {
#     listen 443 ssl http2;
#     listen [::]:443 ssl http2;
#     server_name localhost;
#     root /usr/share/nginx/html;
#     index index.html;
# 
#     # SSL certificates
#     ssl_certificate /etc/nginx/ssl/cert.pem;
#     ssl_certificate_key /etc/nginx/ssl/key.pem;
# 
#     # SSL configuration
#     ssl_protocols TLSv1.2 TLSv1.3;
#     ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
#     ssl_prefer_server_ciphers off;
#     ssl_session_cache shared:SSL:10m;
#     ssl_session_timeout 10m;
# 
#     # HSTS (HTTP Strict Transport Security)
#     add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
# 
#     # OCSP stapling
#     ssl_stapling on;
#     ssl_stapling_verify on;
# 
#     # Include other configurations
#     include /etc/nginx/conf.d/security.conf;
#     include /etc/nginx/conf.d/cache.conf;
# 
#     # PWA specific headers for HTTPS
#     location /manifest.webmanifest {
#         add_header Content-Type application/manifest+json;
#         add_header Cache-Control "public, max-age=604800";
#         expires 7d;
#     }
# 
#     # Service Worker for HTTPS
#     location /sw.js {
#         add_header Content-Type application/javascript;
#         add_header Cache-Control "no-cache, no-store, must-revalidate";
#         add_header Pragma "no-cache";
#         expires 0;
#     }
# 
#     # Handle SPA routing
#     location / {
#         try_files $uri $uri/ /index.html;
#     }
# 
#     # Health check
#     location /health {
#         access_log off;
#         return 200 "healthy\n";
#         add_header Content-Type text/plain;
#     }
# }
# 
# # Redirect HTTP to HTTPS
# server {
#     listen 80;
#     listen [::]:80;
#     server_name localhost;
#     return 301 https://$server_name$request_uri;
# }
