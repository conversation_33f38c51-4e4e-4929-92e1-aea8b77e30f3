<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { useLocale } from '@/composables/useLocale';
import * as ROUTES from '@/constants/routes';
import { LeaveOfType } from '@/enums/leave';
import type { LeaveBalance } from '@/interfaces/leave';
import {
  getLocalizedPath,
  navigateToLocalizedRoute,
} from '@/utils/localized-navigation';
import { NProgress } from 'naive-ui';
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const { locale } = useLocale();

interface Props {
  items: LeaveBalance[];
}

const props = defineProps<Props>();

const leaveTypeColors = {
  [LeaveOfType.SICK_LEAVE]: '#dc2626',
  [LeaveOfType.MATERNITY_LEAVE]: '#8b5cf6',
  [LeaveOfType.UNPAID_LEAVE]: '#f59e0b',
  [LeaveOfType.ANNUAL_LEAVE]: '#10b981',
};

const leaveTypeMap = new Map<number, string>(
  Object.entries(LeaveOfType)
    .filter(([key]) => isNaN(Number(key)))
    .map(([key, value]) => [value as number, key]),
);

const getLeaveTypeByValue = (value: number) => leaveTypeMap.get(value) || '';

const leaveBalances = computed(() =>
  props.items.map((item) => {
    const typeKey =
      item.type_of_leave_name || getLeaveTypeByValue(item.type_of_leave);
    const upperType = typeKey.toUpperCase();

    return {
      type: item.type_of_leave_name || getLeaveTypeByValue(item.type_of_leave),
      remaining: item.remaining_days,
      total: item.total_days,
      used: item.used_days,
      color:
        leaveTypeColors[item.type_of_leave as keyof typeof leaveTypeColors] ||
        '#4f46e5',
      label: t(`leaves.leave_type.${upperType}`),
    };
  }),
);

const calculatePercentage = (used: number, total: number) => {
  if (total === 0) return 0;
  return Math.min(Math.round((used / total) * 100), 100);
};

const historyRoute = getLocalizedPath(ROUTES.LEAVES_HISTORY, locale.value);
</script>

<template>
  <div class="flex w-full flex-col gap-6">
    <div class="flex items-center justify-between text-gray-800 capitalize">
      <p class="text-lg font-semibold">{{ t('leaves.balance.title') }}</p>
      <router-link :to="historyRoute" class="text-sm font-medium capitalize underline underline-offset-2">
        {{ t('leaves.history.title') }}
      </router-link>
    </div>

    <div class="grid gap-4">
      <div v-for="(balance, index) in leaveBalances" :key="index" class="rounded-md border border-gray-200 p-4">
        <div class="mb-2 flex items-center justify-between">
          <span class="font-medium">
            {{ balance.label }}
          </span>
          <span class="text-sm font-semibold">
            {{ balance.remaining }}/{{ balance.total }} ({{ balance.used }}
            {{ t('leaves.common.used') }})
          </span>
        </div>
        <div class="h-2.5 w-full rounded-full bg-gray-200">
          <NProgress :percentage="calculatePercentage(balance.used, balance.total)" :color="balance.color" :height="10"
            :show-indicator="false" type="line" rail-color="#f0f0f0" border-radius="9999px" />
        </div>
      </div>
    </div>

    <Button size="lg" @click="navigateToLocalizedRoute(ROUTES.NEW_LEAVE_APPLICATIONS, locale)">
      {{ t('leaves.balance.new_request') }}
    </Button>
  </div>
</template>
