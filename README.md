# .

This template should help get you started developing with Vue 3 in Vite.

## Recommended IDE Setup

[VSCode](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (and disable Vetur).

## Type Support for `.vue` Imports in TS

TypeScript cannot handle type information for `.vue` imports by default, so we replace the `tsc` CLI with `vue-tsc` for type checking. In editors, we need [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) to make the TypeScript language service aware of `.vue` types.

## Customize configuration

See [Vite Configuration Reference](https://vite.dev/config/).

## Project Setup

```sh
bun install
```

### Compile and Hot-Reload for Development

```sh
bun dev
```

### Type-Check, Compile and Minify for Production

```sh
bun run build
```

### Lint with [ESLint](https://eslint.org/)

```sh
bun lint
```

## Git Hooks with <PERSON><PERSON>

This project uses [<PERSON><PERSON>](https://typicode.github.io/husky/) to manage Git hooks for ensuring code quality:

### 🔍 Pre-commit Hook
- Runs `npm run lint` to check for code errors
- Runs `npm run build-only` to ensure code compiles successfully (without type-check)
- If lint or build fails, the commit will be rejected

### 📝 Commit Message Hook
- Uses **commitlint** with `@commitlint/config-conventional`
- Validates commit message format according to Conventional Commits standard
- Required format: `type(scope): description`
- Valid types: `feat`, `fix`, `docs`, `style`, `refactor`, `test`, `chore`, `perf`, `ci`, `build`, `revert`

**Valid commit message examples:**
```
feat(auth): add login functionality
fix: resolve navigation bug
docs(readme): update installation guide
chore: update dependencies
style: format code with prettier
```

### 🚀 Pre-push Hook
- Runs `npm run type-check` to check TypeScript errors
- Only warns about type errors, doesn't block push

### Test Hooks
To test all hooks:
```sh
./test-hooks.sh
```

### Skip hooks (not recommended)
```sh
# Skip pre-commit and commit-msg
git commit --no-verify -m "your message"

# Skip pre-push
git push --no-verify
```

## let's started
