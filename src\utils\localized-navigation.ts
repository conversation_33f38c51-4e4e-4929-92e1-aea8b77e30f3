import { SUPPORTED_LOCALES, type SupportedLocale } from '@/constants/locales';
import i18n from '@/plugins/i18n';
import router from '@/router';
import VueCookies from 'vue-cookies';
import { toast } from 'vue-sonner';

/**
 * Get the current locale from the route path or cookies
 * @returns The current locale or the default locale ('en')
 */
export function getCurrentLocale(): SupportedLocale {
  try {
    // First try to get locale from current route
    if (router.currentRoute.value) {
      const path = router.currentRoute.value.path;
      const pathParts = path.split('/').filter(Boolean);
      const firstPart = pathParts[0];

      if (
        firstPart &&
        SUPPORTED_LOCALES.includes(firstPart as SupportedLocale)
      ) {
        return firstPart as SupportedLocale;
      }
    }

    // If not found in route, try to get from cookies
    const cookieLocale = VueCookies.get('locale');
    if (
      cookieLocale &&
      SUPPORTED_LOCALES.includes(cookieLocale as SupportedLocale)
    ) {
      return cookieLocale as SupportedLocale;
    }

    // Default to first supported locale
    return SUPPORTED_LOCALES[0];
  } catch (error) {
    toast.error(i18n.global.t('common.toast.error.get_locale'), {
      description: error instanceof Error ? error.message : String(error),
    });
    return SUPPORTED_LOCALES[0];
  }
}

/**
 * Get a localized path for a given base path
 * @param basePath - The base path without locale prefix
 * @param locale - Optional locale to use (defaults to current locale)
 * @returns The localized path with the locale prefix
 */
export function getLocalizedPath(
  basePath: string,
  locale?: SupportedLocale,
): string {
  // Use provided locale or get current locale
  const currentLocale = locale || getCurrentLocale();

  // Ensure basePath starts with a slash
  const normalizedPath = basePath.startsWith('/') ? basePath : `/${basePath}`;

  // Return the path with the locale prefix
  return `/${currentLocale}${normalizedPath}`;
}

/**
 * Navigate to a localized route
 * @param basePath - The base path without locale prefix
 * @param locale - Optional locale to use (defaults to current locale)
 */
export function navigateToLocalizedRoute(
  basePath: string,
  locale?: SupportedLocale,
): void {
  const localizedPath = getLocalizedPath(basePath, locale);

  // Navigate to the localized path
  router.push(localizedPath);
}

/**
 * Extract locale from a path
 * @param path - The path to extract locale from
 * @returns The locale if found, otherwise undefined
 */
export function getLocaleFromPath(path: string): SupportedLocale | undefined {
  const pathParts = path.split('/').filter(Boolean);
  const firstPart = pathParts[0];

  if (firstPart && SUPPORTED_LOCALES.includes(firstPart as SupportedLocale)) {
    return firstPart as SupportedLocale;
  }

  return undefined;
}

/**
 * Change the locale for the current route
 * @param newLocale - The new locale to set
 */
export function changeLocale(newLocale: SupportedLocale): void {
  if (!SUPPORTED_LOCALES.includes(newLocale)) {
    return;
  }

  const currentRoute = router.currentRoute.value;
  if (!currentRoute) {
    return;
  }

  // Get the current path without the locale prefix
  let path = currentRoute.path;
  const currentLocale = getLocaleFromPath(path);

  if (currentLocale) {
    // Remove the current locale prefix
    const pathParts = path.split('/').filter(Boolean);
    pathParts.shift(); // Remove the locale part
    path = pathParts.length > 0 ? '/' + pathParts.join('/') : '/';
  }

  // Create the new path with the selected locale
  const newPath = `/${newLocale}${path === '/' ? '' : path}`;

  // Save the locale in cookies
  VueCookies.set('locale', newLocale, '30d');

  // Navigate to the new path
  router.replace({
    path: newPath,
    query: currentRoute.query,
    hash: currentRoute.hash,
  });
}
