<script setup lang="ts">
import { useI18n } from 'vue-i18n';

defineProps({
  totalDays: {
    type: Number,
    required: true,
  },
  lateDays: {
    type: Number,
    required: true,
  },
  absentDays: {
    type: Number,
    required: true,
  },
});

const { t } = useI18n();
</script>

<template>
  <div
    class="mt-4 hidden w-full rounded-xl border border-gray-200 bg-white p-4 shadow-sm"
  >
    <div class="grid grid-cols-3 gap-4">
      <div class="text-center">
        <div class="text-2xl font-bold text-emerald-600">{{ totalDays }}</div>
        <div class="mt-1 text-xs text-gray-500">
          {{ t('attendance.summary.working_days') }}
        </div>
      </div>
      <div class="text-center">
        <div class="text-2xl font-bold text-amber-600">{{ lateDays }}</div>
        <div class="mt-1 text-xs text-gray-500">
          {{ t('attendance.summary.late_days') }}
        </div>
      </div>
      <div class="text-center">
        <div class="text-2xl font-bold text-red-600">{{ absentDays }}</div>
        <div class="mt-1 text-xs text-gray-500">
          {{ t('attendance.summary.quited_days') }}
        </div>
      </div>
    </div>
  </div>
</template>
