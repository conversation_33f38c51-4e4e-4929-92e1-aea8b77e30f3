<template>
  <div class="bg-gray-50 rounded-lg p-4">
    <div class="flex items-center justify-between mb-3">
      <div class="flex items-center space-x-3">
        <span class="px-3 py-1 rounded-full text-sm font-medium" :class="getShiftColor(shift.code)">
          {{ shift.code }}
        </span>
        <Clock class="w-4 h-4 text-gray-500" />
      </div>
    </div>
    <h3 class="font-semibold text-gray-800 mb-2">{{ shift.name }}</h3>
    <div class="grid grid-cols-2 gap-4 text-sm">
      <div class="flex items-center space-x-2">
        <Sun class="w-4 h-4 text-green-500" />
        <span class="text-gray-600"> {{ t('attendance.details.time_in') }}: {{ shift.start_time }}</span>
      </div>
      <div class="flex items-center space-x-2">
        <Moon class="w-4 h-4 text-blue-500" />
        <span class="text-gray-600"> {{ t('attendance.details.time_out') }}: {{ shift.end_time }}</span>
      </div>
    </div>
    <div class="mt-3 flex items-center space-x-2">
      <Timer class="w-4 h-4 text-orange-500" />
      <span class="text-sm text-gray-600">
        {{ t('attendance.details.total_time') }}: {{ calculateDuration(shift.start_time, shift.end_time) }}
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Clock, Sun, Moon, Timer } from 'lucide-vue-next'
import type { Shift } from '@/interfaces/calendar'
import { useI18n } from 'vue-i18n'

interface Props {
  shift: Shift
  getShiftColor: (shiftCode: string) => string
  getShiftTypeStyle: (type: string) => string
  calculateDuration: (startTime: string, endTime: string) => string
}

const { t } = useI18n()
defineProps<Props>()
</script>
