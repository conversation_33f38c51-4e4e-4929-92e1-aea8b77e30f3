<script setup lang="ts">
import {
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
} from '@/components/ui/drawer';
import { Separator } from '@/components/ui/separator';
import { useUserStore } from '@/stores/user';
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const userStore = useUserStore();

const detailsList = computed(() => [
  { label: t('profile.mobile'), content: userStore.user?.phone_number || '-' },
  { label: t('profile.email'), content: userStore.user?.email || '-' },
  {
    label: t('profile.address'), content: userStore.user?.current_address || '-',
  },
]);
</script>

<template>
  <DrawerContent>
    <DrawerHeader>
      <DrawerTitle class="text-center">{{
        t('profile.contact_information')
        }}</DrawerTitle>
      <DrawerDescription class="hidden" />
    </DrawerHeader>
    <Separator />
    <div class="flex w-full flex-col items-center justify-center gap-4 p-4">
      <div v-for="(item, index) in detailsList" :key="index" class="flex w-full items-center justify-between gap-x-2">
        <div class="text-sm text-gray-600">{{ item.label }}</div>
        <div class="font-medium text-gray-900">{{ item.content }}</div>
      </div>
    </div>
  </DrawerContent>
</template>
