<script setup lang="ts">
import type { CalendarDay } from '@/interfaces/calendar'
import { useI18n } from 'vue-i18n';

interface Props {
  daysOfWeek: string[]
  calendarDays: CalendarDay[]
  getShiftColor: (shiftCode: string) => string
}

defineProps<Props>()

interface Emits {
  (e: 'selectDay', day: CalendarDay): void
}
const { t } = useI18n()

defineEmits<Emits>()
</script>

<template>
  <!-- Days of week header -->
  <div class="flex flex-wrap justify-between mb-2">
    <div v-for="day in daysOfWeek" :key="day"
      class="text-center text-sm font-medium text-gray-500 py-2 basis-[calc(100%/7-6px)]">
      {{ day }}
    </div>
  </div>
  <!-- Calendar days -->
  <div class="flex flex-wrap justify-between">
    <button v-for="day in calendarDays" :key="day.date.toISOString()" @click="$emit('selectDay', day)"
      class="flex flex-col items-center justify-center px-1 py-3 rounded-lg cursor-pointer transition-colors basis-[calc(100%/7-6px)] xs:basis-[calc(100%/7-4px)] mb-1.5"
      :class="{
        'bg-blue-500 text-white': day.is_today,
        'bg-emerald-100 border border-emerald-500': day.has_shift && day.is_current_month && !day.is_today,
        'bg-destructive/25': !day.has_shift && day.is_current_month && !day.is_today,
      }" :disabled="!day.is_current_month">
      <span class="text-sm font-medium" :class="{
        'text-white': day.is_today,
        'text-gray-900': day.is_current_month && !day.is_today,
        'text-gray-400': !day.is_current_month
      }">
        {{ day.day }}
      </span>
      <div v-if="day.is_current_month" class="flex items-center gap-0.5 mt-0.5 justify-center">
        <!-- <span v-if="day.is_today" class="text-[0.65rem] px-0.5 py-0 rounded text-white">HO</span> -->
        <span class="text-[0.65rem] px-0.5 py-0 rounded text-gray-800">HO</span>
      </div>
    </button>
  </div>
  <div class="border-t mt-4">
  </div>
  <div class="mt-2 px-2 py-2 flex justify-evenly">
    <div class="flex items-center space-x-2">
      <p class="p-1 rounded-full bg-emerald-400"></p>
      <p class="text-sm">{{ t('attendance.work_schedule.present') }}</p>
    </div>
    <div class="flex items-center space-x-2">
      <p class="p-1 rounded-full bg-destructive/50"></p>
      <p class="text-sm">{{ t('attendance.work_schedule.absent') }}</p>
    </div>
  </div>
</template>
