import { RequestType, Status } from '@/enums';
import {
  fetchAttendanceRequests,
  fetchLeaveRequests,
} from '@/helpers/management-request-helpers';
import {
  mockManagementRequests,
  type ManagementRequest,
} from '@/mocks/management-requests';
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { toast } from 'vue-sonner';

export const useManagementRequests = () => {
  const { t } = useI18n();

  // Computed properties
  const requests = computed(() => mockManagementRequests.value);

  const leaveRequests = computed(() => fetchLeaveRequests());

  const attendanceRequests = computed(() => fetchAttendanceRequests());

  // Filter methods
  const getFilteredRequests = (
    requests: ManagementRequest[],
    statusFilter: Status | 'all',
  ) => {
    if (statusFilter === 'all') {
      return requests;
    }
    return requests.filter((request) => request.status === statusFilter);
  };

  const getFilteredLeaveRequests = (statusFilter: Status | 'all') => {
    return getFilteredRequests(leaveRequests.value, statusFilter);
  };

  const getFilteredAttendanceRequests = (statusFilter: Status | 'all') => {
    return getFilteredRequests(attendanceRequests.value, statusFilter);
  };

  // Methods
  const findRequestById = (id: number): ManagementRequest | undefined => {
    return requests.value.find((r) => r.id === id);
  };

  const approveRequest = (id: number) => {
    const request = findRequestById(id);
    if (request) {
      request.status = Status.APPROVED;
      toast.success(t('management.requests.messages.approve_success'));
    }
  };

  const rejectRequest = (id: number, reason: string) => {
    const request = findRequestById(id);
    if (request) {
      request.status = Status.REJECTED;
      request.rejectionReason = reason;
      toast.success(t('management.requests.messages.reject_success'));
    }
  };

  const resetRequest = (id: number) => {
    const request = findRequestById(id);
    if (request) {
      request.status = Status.PENDING;
      delete request.rejectionReason;
      toast.success(t('management.requests.messages.reset_success'));
    }
  };

  const getStatusText = (status: Status): string => {
    switch (status) {
      case Status.PENDING:
        return t('common.status.pending');
      case Status.APPROVED:
        return t('common.status.approved');
      case Status.REJECTED:
        return t('common.status.rejected');
      default:
        return '';
    }
  };

  const getStatusColor = (status: Status): string => {
    switch (status) {
      case Status.PENDING:
        return 'warning';
      case Status.APPROVED:
        return 'success';
      case Status.REJECTED:
        return 'error';
      default:
        return 'default';
    }
  };

  const getTypeText = (type: RequestType): string => {
    switch (type) {
      case RequestType.REQUISITION:
        return t('management.requests.types.leave');
      case RequestType.ADDITIONAL:
        return t('management.requests.types.overtime');
      default:
        return '';
    }
  };

  const getTypeColor = (type: RequestType): string => {
    switch (type) {
      case RequestType.REQUISITION:
        return 'primary';
      case RequestType.ADDITIONAL:
        return 'info';
      default:
        return 'default';
    }
  };

  return {
    requests,
    leaveRequests,
    attendanceRequests,
    getFilteredRequests,
    getFilteredLeaveRequests,
    getFilteredAttendanceRequests,
    approveRequest,
    rejectRequest,
    resetRequest,
    getStatusText,
    getStatusColor,
    getTypeText,
    getTypeColor,
    Status,
    RequestType,
  };
};
