import type { NavigationGuardNext, RouteLocationNormalized } from 'vue-router';

import { DEFAULT_LOCALE, type SupportedLocale } from '@/constants/locales';
import { LOGIN } from '@/constants/routes';
import { getLocalizedPath } from '@/utils/localized-navigation';
import { useLocalStorage } from '@vueuse/core';
import VueCookies from 'vue-cookies';

export function authMiddleware(
  to: RouteLocationNormalized,
  _from: RouteLocationNormalized,
  next: NavigationGuardNext,
) {
  const cookies = VueCookies;
  const isAuthenticated = cookies.get('auth-token') !== null;

  if (to.meta.requiresAuth && !isAuthenticated) {
    const locale = useLocalStorage<SupportedLocale>('locale', DEFAULT_LOCALE);

    const localizedLoginPath = getLocalizedPath(LOGIN, locale.value);

    next({ path: localizedLoginPath });
    return;
  }

  next();
}
