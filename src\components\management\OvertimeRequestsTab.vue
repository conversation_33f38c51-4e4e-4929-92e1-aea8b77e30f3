<script setup lang="ts">
import RejectModal from '@/components/management/RejectModal.vue';
import { useAttendance } from '@/composables/useAttendance';
import { RequestType, Status } from '@/enums';
import { NSelect, NDatePicker, } from 'naive-ui'
import { computed, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { toast } from 'vue-sonner';
import { Skeleton } from '../ui/skeleton';
import { Button } from '../ui/button';
import type { ApproveOvertimeRequestPayload, GroupOvertimeRequest, IndividualOvertimeRequest } from '@/interfaces/attendance';
import type { EmployeeInfo } from '@/interfaces/staff';
import { Action } from '@/enums/leave';
import { useLeaveForm } from '@/composables/useLeaveForm';
import { useDateFormats } from '@/composables/useDateFormats';
import { Label } from '../ui/label';

type OvertimeRequestDisplay = {
  id: string | number;
  type: RequestType.ADDITIONAL;
  subType: 'group' | 'individual';
  employeeName: string;
  date: string;
  status: number;
  details: string;
  rejectionReason?: string;
  canApprove: boolean;
  canReject: boolean;
  timeIn: string;
  timeOut: string;
  timekeepingValue: number;
  groupCode?: string;
  employees?: EmployeeInfo[];
  originalData: GroupOvertimeRequest | IndividualOvertimeRequest;
};


defineEmits<{
  'update:statusFilter': [value: Status | 'all'];
}>();

const { managementOvertimeQuery, approveOvertimeRequestMutation } =
  useAttendance();
const { t } = useI18n();
const overtimeFilters = ref({
  page: 1,
  limit: 20,
});
const overtimeRequestsQuery = managementOvertimeQuery(overtimeFilters);

// Transform overtime data for display
const requests = computed((): OvertimeRequestDisplay[] => {
  if (!overtimeRequestsQuery.data.value?.data) return [];

  return overtimeRequestsQuery.data.value.data.map((request: GroupOvertimeRequest | IndividualOvertimeRequest) => {
    if ('group_info' in request) {
      return {
        id: request.group_info.group_code,
        type: RequestType.ADDITIONAL,
        subType: 'group' as const,
        employeeName: request.group_info.creator_info.full_name,
        date: request.group_info.additional_day,
        status: request.group_info.status,
        details: `${t('management.requests.overtime.group_request')}: ${request.group_info.reason} (${request.group_info.total_employees} ${t('management.requests.overtime.employees')})`,
        rejectionReason: request.group_info.reject_reason || '',
        canApprove: request.group_info.can_approve,
        canReject: request.group_info.can_reject,
        timeIn: request.group_info.time_in,
        timeOut: request.group_info.time_out,
        timekeepingValue: request.group_info.timekeeping_value,
        groupCode: request.group_info.group_code,
        employees: request.employees,
        originalData: request,
      };
    } else {
      // Individual request
      return {
        id: request.id,
        type: RequestType.ADDITIONAL,
        subType: 'individual' as const,
        employeeName: request.employee_info.full_name,
        date: request.additional_day,
        status: request.status,
        details: `${t('management.requests.overtime.individual_request')}: ${request.reason}`,
        rejectionReason: request.reject_reason || '',
        canApprove: request.can_approve,
        canReject: request.can_reject,
        timeIn: request.time_in,
        timeOut: request.time_out,
        timekeepingValue: request.timekeeping_value,
        originalData: request,
      };
    }
  });
});
const filteredRequests = computed(() => {
  return requests.value.filter((request) => {
    const matchesStatus =
      filterState.value.status === 'all' || request.status === filterState.value.status;

    const matchesApprover =
      filterState.value.approver === null ||
      (
        request.subType === 'group'
          ? (request.originalData as GroupOvertimeRequest).group_info.approver === filterState.value.approver
          : (request.originalData as IndividualOvertimeRequest).approver === filterState.value.approver
      );
    // Enhanced date filtering
    let matchesDate = true;
    const requestDate = new Date(request.date).setHours(0, 0, 0, 0);

    // Single date filter
    if (filterState.value.date !== null) {
      matchesDate = requestDate === new Date(filterState.value.date).setHours(0, 0, 0, 0);
    }

    // Date range filter
    if (filterState.value.dateRange !== null) {
      const [startDate, endDate] = filterState.value.dateRange;
      const start = new Date(startDate).setHours(0, 0, 0, 0);
      const end = new Date(endDate).setHours(23, 59, 59, 999);
      matchesDate = requestDate >= start && requestDate <= end;
    }

    // Quick date filter
    if (filterState.value.quickDate !== null) {
      const quickRange = getQuickDateRange(filterState.value.quickDate);
      if (quickRange) {
        const [start, end] = quickRange;
        const startTime = start.setHours(0, 0, 0, 0);
        const endTime = end.setHours(23, 59, 59, 999);
        matchesDate = requestDate >= startTime && requestDate <= endTime;
      }
    }

    const matchesSubType =
      filterState.value.subType === 'all' || request.subType === filterState.value.subType;


    return matchesStatus && matchesApprover && matchesDate && matchesSubType;
  });
});

// Handle approve request
const handleApprove = async (request: OvertimeRequestDisplay) => {
  try {
    const payload: ApproveOvertimeRequestPayload = {
      action: Action.APPROVE,
    };

    if (request.subType === 'group') {
      // For group requests, use group_code
      payload.group_code = String(request.groupCode);
    } else {
      // For individual requests, use request_ids
      payload.id = Number(request.id);
    }

    const response = await approveOvertimeRequestMutation.mutateAsync(payload);

    if (response.success) {
      toast.success(t('management.requests.messages.approve_success'));
    } else {
      toast.error(response.message || t('common.error.something_went_wrong'));
    }
  } catch (error: any) {
    console.error('Error approving request:', error);
    const errorMessage =
      error?.response?.data?.message ||
      error?.message ||
      t('common.error.something_went_wrong');
    toast.error(errorMessage);
  }
};

// Handle reject request
const handleReject = async (
  request: OvertimeRequestDisplay,
  reason: string,
) => {
  try {
    const payload: ApproveOvertimeRequestPayload = {
      action: Action.REJECT,
      reject_reason: reason,
    };

    if (request.subType === 'group') {
      // For group requests, use group_code
      payload.group_code = String(request.groupCode);
    } else {
      // For individual requests, use request_ids
      payload.id = Number(request.id);
    }

    const response = await approveOvertimeRequestMutation.mutateAsync(payload);

    if (response.success) {
      toast.success(t('management.requests.messages.reject_success'));
    } else {
      toast.error(response.message || t('common.error.something_went_wrong'));
    }
  } catch (error: any) {
    console.error('Error rejecting request:', error);
    const errorMessage =
      error?.response?.data?.message ||
      error?.message ||
      t('common.error.something_went_wrong');
    toast.error(errorMessage);
  }
};

// Modal states for rejection
const showRejectModal = ref(false);
const selectedRequest = ref<OvertimeRequestDisplay | null>(null);

const openRejectModal = (request: OvertimeRequestDisplay) => {
  selectedRequest.value = request;
  showRejectModal.value = true;
};

const closeRejectModal = () => {
  showRejectModal.value = false;
  selectedRequest.value = null;
};

const confirmReject = (reason: string) => {
  if (selectedRequest.value) {
    handleReject(selectedRequest.value, reason);
    closeRejectModal();
  }
};

const formatTime = (time: string) => {
  if (!time) return '';
  return new Date(`2000-01-01T${time}`).toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
  });
};

const getStatusText = (status: Status): string => {
  switch (status) {
    case Status.PENDING:
      return t('common.status.pending');
    case Status.APPROVED:
      return t('common.status.approved');
    case Status.REJECTED:
      return t('common.status.rejected');
    default:
      return '';
  }
};

const getStatusColor = (status: Status): string => {
  switch (status) {
    case Status.PENDING:
      return 'amber';
    case Status.APPROVED:
      return 'emerald';
    case Status.REJECTED:
      return 'red';
    default:
      return 'gray';
  }
};
const { dateFormats } = useDateFormats();


const { staffApproveOptions } = useLeaveForm()

const subTypeOptions = [
  { label: t('management.requests.overtime.all'), value: 'all' },
  { label: t('management.requests.overtime.group'), value: 'group' },
  { label: t('management.requests.overtime.individual'), value: 'individual' },
];

const statusOptions = [
  { label: t('attendance.status.all'), value: "all" },
  { label: t('attendance.status.pending'), value: Status.PENDING },
  { label: t('attendance.status.approved'), value: Status.APPROVED },
  { label: t('attendance.status.rejected'), value: Status.REJECTED },
];
const filterState = ref({
  status: 'all' as Status | 'all',
  approver: null as number | null,
  date: null as number | null,
  dateRange: null as [number, number] | null, // Date range filter
  quickDate: null as string | null, // Quick date filter
  subType: 'all' as 'all' | 'group' | 'individual',
});

// Helper function to get date range based on quick filter
const getQuickDateRange = (quickFilter: string): [Date, Date] | null => {
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

  switch (quickFilter) {
    case 'today':
      return [today, today];
    case 'yesterday':
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      return [yesterday, yesterday];
    case 'this_week':
      const startOfWeek = new Date(today);
      startOfWeek.setDate(today.getDate() - today.getDay());
      const endOfWeek = new Date(startOfWeek);
      endOfWeek.setDate(startOfWeek.getDate() + 6);
      return [startOfWeek, endOfWeek];
    case 'last_week':
      const lastWeekStart = new Date(today);
      lastWeekStart.setDate(today.getDate() - today.getDay() - 7);
      const lastWeekEnd = new Date(lastWeekStart);
      lastWeekEnd.setDate(lastWeekStart.getDate() + 6);
      return [lastWeekStart, lastWeekEnd];
    case 'this_month':
      const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
      const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);
      return [startOfMonth, endOfMonth];
    case 'last_month':
      const lastMonthStart = new Date(today.getFullYear(), today.getMonth() - 1, 1);
      const lastMonthEnd = new Date(today.getFullYear(), today.getMonth(), 0);
      return [lastMonthStart, lastMonthEnd];
    default:
      return null;
  }
};

// Watch for filter conflicts and clear others when one is selected
watch(() => filterState.value.date, (newDate) => {
  if (newDate !== null) {
    filterState.value.dateRange = null;
    filterState.value.quickDate = null;
  }
});

watch(() => filterState.value.dateRange, (newRange) => {
  if (newRange !== null) {
    filterState.value.date = null;
    filterState.value.quickDate = null;
  }
});

watch(() => filterState.value.quickDate, (newQuick) => {
  if (newQuick !== null) {
    filterState.value.date = null;
    filterState.value.dateRange = null;
  }
});
</script>

<template>
  <div class="overtime-requests-tab">
    <div class="space-y-4 mb-6">
      <!-- Row 1: & Status -->
      <div class="grid grid-cols-2 md:grid-cols-2 gap-3">
        <div class="space-y-1.5">
          <Label class="font-medium text-sm text-muted-foreground">{{ t('attendance.filters.status') }}</Label>
          <n-select v-model:value="filterState.status" default-value="all" filterable :options="statusOptions"
            :placeholder="t('attendance.filter.all_status')" />
        </div>
        <div class="space-y-1.5">
          <Label class="font-medium text-sm text-muted-foreground">
            {{ t('management.requests.overtime.registration_type') }}
          </Label>
          <n-select v-model:value="filterState.subType" :options="subTypeOptions"
            :placeholder="t('management.requests.overtime.request_type')" />
        </div>
      </div>

      <!-- Row 2: Approver & Quick Date Filter -->
      <div class="grid grid-cols-2 md:grid-cols-2 gap-3">
        <div class="space-y-1.5">
          <Label class="font-medium text-sm text-muted-foreground">{{ t('attendance.filters.creator') }}</Label>
          <n-select v-model:value="filterState.approver" filterable :options="staffApproveOptions"
            :placeholder="t('attendance.filters.creator_placeholder')" clearable />
        </div>

        <div class="space-y-1.5">
          <Label class="font-medium text-sm text-muted-foreground">{{ t('attendance.filter.select_date') }}</Label>
          <n-date-picker v-model:value="filterState.dateRange" type="daterange" :format="dateFormats.date.display"
            :placeholder='t("management.requests.overtime.date_placeholder")' clearable class="w-full" />
        </div>

      </div>

      <!-- Clear Filters Button -->
      <div class="flex justify-end pt-2">
        <Button variant="outline" size="sm"
          @click="filterState = { status: 'all', approver: null, date: null, dateRange: null, quickDate: null, subType: 'all' }">
          {{ t('attendance.filter.clear_filter') }}
        </Button>
      </div>
    </div>
    <!-- Skeleton Loading Cards -->
    <div v-if="
      overtimeRequestsQuery.isLoading.value ||
      approveOvertimeRequestMutation.isPending.value
    " class="space-y-4">
      <div v-for="n in 3" :key="n" class="rounded-lg border bg-white p-4 shadow-sm space-y-3">
        <div class="flex justify-between items-center">
          <div>
            <Skeleton class="h-5 w-40" />
            <Skeleton class="h-4 w-24 mt-1" />
            <Skeleton class="h-4 w-32 mt-1" />
          </div>
          <div class="flex gap-2">
            <Skeleton class="h-6 w-16 rounded" />
            <Skeleton class="h-6 w-20 rounded" />
          </div>
        </div>
        <Skeleton class="h-4 w-full" />
        <Skeleton class="h-4 w-2/3" />
        <div class="flex gap-2 pt-2">
          <Skeleton class="h-8 w-24 rounded" />
          <Skeleton class="h-8 w-24 rounded" />
        </div>
      </div>
    </div>
    <div v-if="filteredRequests.length === 0" class="py-8 text-center text-gray-500">
      <p>{{ t('management.requests.no_overtime_requests') }}</p>
    </div>
    <!-- Requests List -->
    <div v-else class="space-y-4">
      <div v-for="request in filteredRequests" :key="request.id"
        class="rounded-lg border bg-white p-4 shadow-sm hover:shadow-md">
        <div class="mb-3 flex items-start justify-between">
          <div>
            <h3 class="text-lg font-semibold">{{ request.employeeName }}</h3>
            <p class="text-sm text-gray-600">{{ request.date }}</p>
            <div class="mt-1 flex items-center gap-2 text-xs text-gray-500">
              <span>{{ formatTime(request.timeIn) }} -
                {{ formatTime(request.timeOut) }}</span>
              <span>•</span>
              <span>{{ request.timekeepingValue }}h</span>
            </div>
          </div>
          <div class="flex gap-2">
            <span :class="[
              'rounded px-2 py-1 text-xs font-medium',
              `bg-${getStatusColor(request.status)}-100 text-${getStatusColor(request.status)}-600 border border-${getStatusColor(request.status)}-500`,
            ]">
              {{ getStatusText(request.status) }}
            </span>
            <span :class="[
              'rounded px-2 py-1 text-xs font-medium',
              request.subType === 'group'
                ? 'bg-sky-100 text-sky-700 border border-sky-500'
                : 'bg-purple-100 text-purple-700 border border-purple-500',
            ]">
              {{
                request.subType === 'group'
                  ? t('management.requests.overtime.group')
                  : t('management.requests.overtime.individual')
              }}
            </span>
          </div>
        </div>

        <p class="mb-4 text-gray-700">{{ request.details }}</p>

        <!-- Group Members (for group requests) -->
        <div v-if="request.subType === 'group' && request.employees" class="mb-4 rounded bg-gray-50 p-3">
          <h4 class="mb-2 text-sm font-medium text-gray-700">
            {{ t('management.requests.overtime.group_members') }}:
          </h4>
          <div class="grid grid-cols-1 gap-2">
            <div v-for="employee in request.employees" :key="employee.id" class="flex items-center text-sm">
              <span class="font-medium">{{ employee.full_name }}</span>
              <span class="ml-2 text-gray-500">({{ employee.staff_identifi }})</span>
            </div>
          </div>
        </div>

        <div v-if="request.status === Status.REJECTED && request.rejectionReason"
          class="mb-4 rounded border-l-4 border-red-400 bg-red-50 p-3">
          <p class="text-sm text-red-700">
            <span class="font-medium">{{ t('management.requests.rejection_reason') }}:</span>
            {{ request.rejectionReason }}
          </p>
        </div>

        <!-- Action Buttons -->
        <div class="flex gap-2">
          <Button v-if="request.status === Status.PENDING && request.canApprove" @click="handleApprove(request)"
            :disabled="approveOvertimeRequestMutation.isPending.value" variant="default"
            class="flex-1 rounded-sm cursor-pointer disabled:cursor-not-allowed disabled:opacity-50">
            {{ t('management.requests.actions.approve') }}
          </Button>

          <Button v-if="request.status === Status.PENDING && request.canReject" @click="openRejectModal(request)"
            :disabled="approveOvertimeRequestMutation.isPending.value" variant="outline"
            class="flex-1 cursor-pointer rounded-sm disabled:cursor-not-allowed disabled:opacity-50 hover:text-red-500 hover:border-red-500 hover:bg-red-100 transition-colors duration-200">
            {{ t('management.requests.actions.reject') }}
          </Button>
        </div>
      </div>
      <!-- Empty State -->
    </div>

    <!-- Rejection Modal -->
    <RejectModal v-model:show="showRejectModal" @confirm="confirmReject" @cancel="closeRejectModal" />
  </div>
</template>

<style scoped>
.overtime-requests-tab {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 16px;
}
</style>
