<script setup lang="ts">
import { useLocale } from '@/composables/useLocale';
import { HOME } from '@/constants/routes';
import { getLocalizedPath } from '@/utils/localized-navigation';
import { IonPage } from '@ionic/vue';
import { useI18n } from 'vue-i18n';

const { locale } = useLocale();
const { t } = useI18n();

const homePath = getLocalizedPath(HOME, locale.value);
</script>

<template>
  <ion-page>
    <div class="not-found">
      <h1 class="mb-4 text-4xl font-bold">404</h1>
      <h2 class="mb-6 text-2xl">{{ t('common.not_found.title') }}</h2>
      <p class="mb-8">{{ t('common.not_found.message') }}</p>
      <RouterLink
        :to="homePath"
        class="rounded bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"
      >
        {{ t('common.not_found.go_home') }}
      </RouterLink>
    </div>
  </ion-page>
</template>

<style scoped>
.not-found {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  min-height: 60vh;
}
</style>
