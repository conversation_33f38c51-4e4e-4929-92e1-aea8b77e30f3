<template>
  <div class="px-2 w-full rounded-lg">
    <div class="flex justify-between items-center mb-4">
      <h2 class="text-lg font-semibold">{{ t('attendance.work_schedule.table_notes') }}</h2>
      <div v-if="shifts.length > initialDisplayCount" class=" text-center">
        <button @click="showAllShifts = true"
          class="inline-flex items-center justify-center whitespace-nowrap text-gray-700 text-sm font-medium cursor-pointer hover:underline">
          {{ t('attendance.work_schedule.view_all') }} ({{ shifts.length - initialDisplayCount }}+)
        </button>
      </div>
    </div>
    <div class="space-y-2 h-[200px] overflow-y-auto scroll-container">
      <ShiftCard v-for="shift in visibleShifts" :key="shift.code" :shift="shift" />
    </div>
    <Drawer :open="showAllShifts" @update:open="showAllShifts = $event">
      <DrawerContent>
        <div class="mx-auto w-full max-w-sm">
          <DrawerHeader>
            <DrawerTitle class="text-center">{{ t('attendance.details.all_shifts') }}</DrawerTitle>
            <DrawerDescription class="sr-only">{{ t('attendance.details.desc_shifts') }}</DrawerDescription>
          </DrawerHeader>
          <div class="p-4 pb-0 space-y-2 max-h-60 overflow-y-auto scroll-container">
            <ShiftCard v-for="shift in shifts" :key="shift.code" :shift="shift" />
          </div>
          <DrawerFooter>
            <DrawerClose as-child>
              <Button variant="outline"
                class="w-full mt-4 bg-blue-500 text-white py-2 rounded-lg font-medium hover:bg-blue-600 hover:text-white transition-colors">
                {{ t('attendance.common.close') }}
              </Button>
            </DrawerClose>
          </DrawerFooter>
        </div>
      </DrawerContent>
    </Drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { allShifts } from '@/mocks/shift_data';
import type { ShiftType } from '@/interfaces/calendar';
import ShiftCard from '@/components/ui/shift_note/ShiftCard.vue';
import { useI18n } from 'vue-i18n';

import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
} from '@/components/ui/drawer';
import { Button } from '@/components/ui/button';

const initialDisplayCount = 10;
const showAllShifts = ref(false);
const shifts: ShiftType[] = allShifts;

const visibleShifts = computed<ShiftType[]>(() => {
  return shifts.slice(0, initialDisplayCount);
});

const { t } = useI18n();
</script>
