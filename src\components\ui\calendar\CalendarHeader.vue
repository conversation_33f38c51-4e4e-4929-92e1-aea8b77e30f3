<template>
  <div class="flex items-center w-full justify-between ">
    <button @click="$emit('previousMonth')" class="p-2 hover:bg-gray-100 rounded-lg" aria-label="Last month">
      <ChevronLeft class="w-5 h-5" />
    </button>
    <h1 class="text-base font-semibold">
      {{ capitalizedMonthYear }}
    </h1>
    <button @click="$emit('nextMonth')" class="p-2 hover:bg-gray-100 rounded-lg" aria-label="Next month">
      <ChevronRight class="w-5 h-5" />
    </button>
  </div>
</template>

<script setup lang="ts">
import { ChevronLeft, ChevronRight } from 'lucide-vue-next'
import { useLocale } from '@/composables/useLocale'
import { computed } from 'vue'

interface Props {
  currentDate: Date
}
const { currentDate } = defineProps<Props>()

interface Emits {
  (e: 'previousMonth'): void
  (e: 'nextMonth'): void
}
defineEmits<Emits>()

const { locale } = useLocale()

const capitalizedMonthYear = computed(() => {
  const dateStr = currentDate.toLocaleDateString(locale.value, {
    month: 'long',
    year: 'numeric',
  })
  return dateStr.charAt(0).toUpperCase() + dateStr.slice(1)
})
</script>
