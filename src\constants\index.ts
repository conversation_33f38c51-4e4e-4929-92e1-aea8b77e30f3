import { formatLeaveType, getStatusText } from '@/constants/leave-history';

import { LeaveOfType } from '@/enums/leave';
import { Status } from '@/enums';
import i18n from '@/plugins/i18n';

export const statuses: { label: string; value: Status | 'all' }[] = [
  {
    label: i18n.global.t('common.status.all'),
    value: 'all',
  },
  {
    label: getStatusText(Status.APPROVED),
    value: Status.APPROVED,
  },
  {
    label: getStatusText(Status.PENDING),
    value: Status.PENDING,
  },
  {
    label: getStatusText(Status.REJECTED),
    value: Status.REJECTED,
  },
];

export const leaveTypes: { label: string; value: LeaveOfType | 'all' }[] = [
  {
    label: i18n.global.t('leaves.leave_type.ALL'),
    value: 'all',
  },
  {
    label: formatLeaveType(LeaveOfType.ANNUAL_LEAVE),
    value: LeaveOfType.ANNUAL_LEAVE,
  },
  {
    label: formatLeaveType(LeaveOfType.SICK_LEAVE),
    value: LeaveOfType.SICK_LEAVE,
  },
  {
    label: formatLeaveType(LeaveOfType.UNPAID_LEAVE),
    value: LeaveOfType.UNPAID_LEAVE,
  },
  {
    label: formatLeaveType(LeaveOfType.MATERNITY_LEAVE),
    value: LeaveOfType.MATERNITY_LEAVE,
  },
];

export const PAGE_SIZE = 10;
export const LIMIT = 5;
export const DEFAULT_TOTAL_ITEMS = 0;
export const DEFAULT_TOTAL_PAGE = 1;
