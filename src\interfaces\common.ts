import type { ApiResponse as ApiResponseNoti } from "@/types";

export type NotificationType = 'approve' | 'pending' | 'reject';

export interface NotificationAdditionalData {
  type: string;
  status: number;
  status_text: string;
  approver: string;
  subject: string;
}

export interface Notification {
  id: number;
  description: string;
  link: string;
  additional_data: NotificationAdditionalData;
  from_user_id: number;
  to_user_id: number;
  is_read: number;
  date: string;
  date_formatted: string;
}

// Legacy interface for backward compatibility
export interface LegacyNotification {
  id: number;
  type: NotificationType;
  title: string;
  message: string;
  time: Date;
  read: boolean;
  sender?: {
    name: string;
    avatar?: string;
  };
  link?: string;
}

export interface ApiResponse<T = unknown> {
  status: boolean;
  message?: string;
  data?: T;
}

export interface NotificationData {
  notifications: Notification[];
}

export type NotificationResponse = ApiResponseNoti<NotificationData>;


// Update overtimeresponse to support Pagination

