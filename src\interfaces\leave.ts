import type { LeaveOfType, RelType } from "@/enums/leave";

import type { ApiResponse } from "@/types";
import type { AttendanceStatus } from "@/enums/attendance";
import type { Pagination } from "@/interfaces/attendance";
import type { Status } from "@/enums";

export interface Leave {
  id: number;
  rel_type: RelType;
  subject: string;
  start_time: string;
  end_time: string;
  reason: string;
  type_of_leave: LeaveOfType;
  number_of_leaving_day: number;
  status: Status;
  attachment: boolean;
  can_update: boolean;
  datecreated: string;
  group_code: string | null;
  follower_id: number | null;
  approver_id: number | null;
  is_deducted_attendance?: AttendanceStatus | null;
  group_members?: GroupMember[];
}

export interface LeaveBalance {
  id: number;
  type_of_leave: LeaveOfType;
  type_of_leave_name: string;
  total_days: number;
  used_days: number;
  remaining_days: number;
  percentage_used: number;
}

interface LeaveDaysOff {
  type_of_leave: LeaveOfType;
  type_of_leave_name: string;
  remain: number;
  days_off: number;
}

export interface StaffRemain {
  staff_id: number;
  full_name: string;
  leaves: LeaveDaysOff[];
}

type GroupMember = {
  id: number;
  first_name: string;
  last_name: string;
  staff_identifi: string;
  full_name: string;
};


export interface LeaveRequest {
  rel_type: RelType;
  subject: string;
  start_time: string | null;
  end_time?: string | null;
  reason: string;
  type_of_leave?: LeaveOfType;
  approver_id: number | null;
  follower_id?: number | null;
  number_of_leaving_day?: number;
  attachment?: File | null;
}

export interface LeaveEmployeeInfo {
  id: number;
  staff_id: number;
  full_name: string;
  staff_identifi: string;
  email: string;
}

export interface LeaveCreatorInfo {
  staff_id: number;
  full_name: string;
  staff_identifi: string;
  email: string;
}

export interface GroupLeaveRequest {
  id: number;
  type: string;
  group_code: string;
  registration_type: string;
  subject: string;
  start_time: string;
  end_time: string;
  rel_type: number;
  rel_type_name: string;
  type_of_leave: number;
  leave_type_name: string;
  reason: string;
  status: number;
  status_text: string;
  reject_reason: string;
  number_of_leaving_day: number;
  creator: number;
  approver_id: number | null;
  creator_info: LeaveCreatorInfo;
  can_approve: boolean;
  can_reject: boolean;
  datecreated: string;
  creator_employee: LeaveEmployeeInfo | null;
  employees: LeaveEmployeeInfo[];
  total_employees: number;
  total_employees_including_creator: number;
}

export interface IndividualLeaveRequest {
  id: number;
  type: string;
  group_code: null;
  registration_type: string;
  subject: string;
  start_time: string;
  end_time: string;
  rel_type: number;
  rel_type_name: string;
  type_of_leave: number;
  leave_type_name: string;
  reason: string;
  status: number;
  status_text: string;
  reject_reason: string;
  number_of_leaving_day: number;
  creator: number;
  approver_id: number | null;
  creator_info: LeaveCreatorInfo;
  can_approve: boolean;
  can_reject: boolean;
  datecreated: string;
  staff_id: number;
  employee_info: LeaveEmployeeInfo;
}

export interface ManagementLeaveResponse {
  success: boolean;
  data: (GroupLeaveRequest | IndividualLeaveRequest)[];
  pagination: Pagination;
  total: number;
  filters_applied: Record<string, any>;
  management_info: ManagementLeaveInfo;
}

export interface ManagementLeaveInfo {
  manager_id: number;
  subordinate_count: number;
  can_approve_requests: boolean;
}

export interface ApproveLeaveRequestPayload {
  id?: number;
  group_code?: string;
  action: 'approve' | 'reject';
  reject_reason?: string;
}

export interface LeaveFailedRequest {
  id: number;
  reason: string;
}

export interface ApproveLeaveRequestData {
  action: string;
  total_processed: number;
  total_failed: number;
  processed_requests: LeaveProcessedRequest[];
  failed_requests?: LeaveFailedRequest[];
  group_code?: string;
  reject_reason?: string;
}

export interface ApproveLeaveRequestResponse {
  success: boolean;
  message: string;
  data: ApproveLeaveRequestData;
}

export interface LeaveProcessedRequest {
  id: number;
  employee_name: string;
  start_time: string;
  end_time: string;
  action: string;
  status: number;
}

export interface LeaveStatistics {
  total_requests: number;
  total_approves: number;
  total_pendings: number;
  total_rejecteds: number;
}

export type LeaveMutationResponse = ApiResponse<LeaveRequest>;
export type LeaveDaysOffResponse = ApiResponse<StaffRemain[]>;
export type LeaveBalanceResponse = ApiResponse<LeaveBalance[]>;
export type LeaveResponse = ApiResponse<Leave[]>;
export type LeaveItemResponse = ApiResponse<Leave>;
export type LeaveStatisticsResponse = ApiResponse<LeaveStatistics>;
