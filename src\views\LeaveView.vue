<script setup lang="ts">
import LeaveBalance from '@/components/leaves/LeaveBalance.vue';
import RecentLeave from '@/components/leaves/RecentLeave.vue';
import UpcomingHoliday from '@/components/leaves/UpcomingHoliday.vue';
import { useLeaveRequests } from '@/composables/useLeaveRequests';
import { type LeaveBalance as ILeaveBalance } from '@/interfaces/leave';
import { IonPage } from '@ionic/vue';
import { ref, watchEffect } from 'vue';

const leaveBalances = ref<ILeaveBalance[]>([]);
const { leaveBalanceListQuery } = useLeaveRequests();

watchEffect(() => {
  if (leaveBalanceListQuery.data.value) {
    leaveBalances.value = leaveBalanceListQuery.data.value;
  }
});
</script>

<template>
  <ion-page class="ion-padding">
    <div class="scroll-container flex h-full flex-col items-center gap-y-7">
      <!-- Also fix variable name here -->
      <LeaveBalance :items="leaveBalances" />
      <RecentLeave />
      <UpcomingHoliday />
    </div>
  </ion-page>
</template>
