<template>
  <div class="flex justify-between items-center p-2 border rounded-md bg-gray-50">
    <span class="font-medium text-gray-800">{{ shift.code }}</span>
    <span class="text-sm text-gray-600 flex-1 ml-4">{{ shift.description }}</span>
    <span class="text-sm text-gray-500">{{ shift.time }}</span>
  </div>
</template>

<script setup lang="ts">
import type { ShiftType } from '@/interfaces/calendar';

interface Props {
  shift: ShiftType;
}

defineProps<Props>();
</script>
