<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { useAttendance } from '@/composables/useAttendance';
import { useLocale } from '@/composables/useLocale';
import { useProfile } from '@/composables/useProfile';
import { getStatusClass, getStatusIcon } from '@/constants/leave-history';
import { UPDATE_OVERTIME } from '@/constants/routes';
import { Status } from '@/enums';
import type { Overtime, UpdateEmployeeStatusPayload } from '@/interfaces/attendance';
import { formatDate } from '@/utils/format';
import { navigateToLocalizedRoute } from '@/utils/localized-navigation';
import { getCurrentEmployeeStatus } from '@/utils/employee-status-helper';
import { Check, PencilIcon, X } from 'lucide-vue-next';
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { toast } from 'vue-sonner';

interface Props {
  overtime: Overtime;
}
const props = defineProps<Props>();
const emit = defineEmits<{
  edit: [id: number | string];
}>();

const displayStatus = computed(() => {
  return props.overtime.group_info?.status ?? props.overtime.status;
});

const { t } = useI18n();
const { locale } = useLocale();
const { user } = useProfile();
const { updateStatusOvertimeMutation } = useAttendance();

const isGroup = computed(() => !!props.overtime.group_info);

const currentEmployeeStatus = computed(() => {
  if (!user.value?.id) return null;

  const currentUserId = Number(user.value.id);
  return getCurrentEmployeeStatus(props.overtime, currentUserId);
});

const getStatusBadgeClass = (status: number) => {
  const statusMap = [Status.PENDING, Status.APPROVED, Status.REJECTED] as Record<number, Status>;
  const mappedStatus = statusMap[status] || Status.PENDING;
  return `inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getStatusClass(mappedStatus)}`;
};

const getStatusTextDisplay = (status: number) => {
  const statusMap: Record<number, string> = {
    0: t('attendance.status.pending'),
    1: t('attendance.status.approved'),
    2: t('attendance.status.rejected'),
  };
  return statusMap[status] || t('attendance.common.unknown');
};

const getStatusIconComponent = (status: number) => {
  const statusMap: Record<number, Status> = {
    0: Status.PENDING,
    1: Status.APPROVED,
    2: Status.REJECTED,
  };
  const mappedStatus = statusMap[status] || Status.PENDING;
  return getStatusIcon(mappedStatus);
};

const handleEdit = () => {
  const canEdit = props.overtime.group_info?.can_edit || props.overtime.can_edit;
  if (!canEdit) return;

  const identifier = props.overtime.group_info?.group_code || props.overtime.id;

  emit('edit', identifier);

  navigateToLocalizedRoute(
    UPDATE_OVERTIME.replace(':id', String(identifier)),
    locale.value,
  );
};

// Handle approve request
const handleApprove = async () => {
  try {
    if (!user.value?.id) {
      toast.error(t('common.error.user_not_found'));
      return;
    }

    const currentUserId = Number(user.value.id);
    let currentEmployeeRecord = null;
    if (isGroup.value && props.overtime.employees && Array.isArray(props.overtime.employees)) {
      currentEmployeeRecord = props.overtime.employees.find(
        (emp) => emp.employee_info.staff_id === currentUserId
      );
    }

    if (!currentEmployeeRecord) {
      toast.error(t('common.error.employee_not_found'));
      return;
    }

    const payload: UpdateEmployeeStatusPayload = {
      record_id: currentEmployeeRecord.id,
      employee_status: 1,
    };

    const response = await updateStatusOvertimeMutation.mutateAsync(payload);

    if (response) {
      toast.success(t('management.requests.messages.approve_success'));
    } else {
      toast.error(t('common.error.something_went_wrong'));
    }
  } catch (error: any) {
    console.error('Error approving request:', error);
    const errorMessage =
      error?.response?.data?.message ||
      error?.message ||
      t('common.error.something_went_wrong');
    toast.error(errorMessage);
  }
};

// Handle reject request
const handleReject = async () => {
  try {
    const reason = prompt(t('management.requests.reject_reason_prompt'));
    if (!reason) return;

    if (!user.value?.id) {
      toast.error(t('common.error.user_not_found'));
      return;
    }

    const currentUserId = Number(user.value.id);

    let currentEmployeeRecord = null;
    if (isGroup.value && props.overtime.employees && Array.isArray(props.overtime.employees)) {
      currentEmployeeRecord = props.overtime.employees.find(
        (emp) => emp.employee_info.staff_id === currentUserId
      );
    }

    if (!currentEmployeeRecord) {
      toast.error(t('common.error.employee_not_found'));
      return;
    }
    const payload: UpdateEmployeeStatusPayload = {
      record_id: currentEmployeeRecord.id,
      employee_status: 2,
      reject_reason: reason,
    };

    const response = await updateStatusOvertimeMutation.mutateAsync(payload);

    if (response) {
      toast.success(t('management.requests.messages.reject_success'));
    } else {
      toast.error(t('common.error.something_went_wrong'));
    }
  } catch (error: any) {
    console.error('Error rejecting request:', error);
    const errorMessage =
      error?.response?.data?.message ||
      error?.message ||
      t('common.error.something_went_wrong');
    toast.error(errorMessage);
  }
};
</script>

<template>
  <div class="rounded-lg border border-gray-200 bg-white p-4 shadow-sm transition-shadow hover:shadow-md">
    <div class="mb-3 flex items-center justify-between">
      <h3 class="text-base font-semibold text-gray-900">
        {{ isGroup
          ? $t('attendance.overtime.card.group')
          : $t('attendance.overtime.card.individual') }}
      </h3>
      <span :class="getStatusBadgeClass(displayStatus)">
        <component :is="getStatusIconComponent(displayStatus)" class="mr-1 h-3 w-3" />
        {{ getStatusTextDisplay(displayStatus) }}
      </span>
    </div>

    <!-- Main Info Grid -->
    <div class="mb-3 grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
      <div class="flex items-center justify-between">
        <span class="text-gray-500">
          {{ $t('attendance.overtime.card.date') }}:
        </span>
        <span class="font-medium text-gray-900">
          {{ formatDate(overtime.additional_day, locale) }}
        </span>
      </div>

      <div class="flex items-center justify-between">
        <span class="text-gray-500">
          {{ $t('attendance.overtime.card.duration') }}:
        </span>
        <span class="font-medium text-gray-900">
          {{
            isGroup
              ? overtime.group_info?.timekeeping_value
              : overtime.timekeeping_value
          }}{{ $t('attendance.overtime.card.hours_suffix') }}
        </span>
      </div>

      <div class="col-span-2 flex items-center justify-between">
        <span class="text-gray-500">
          {{ $t('attendance.overtime.card.time') }}:
        </span>
        <span class="font-medium text-gray-900">
          {{
            isGroup
              ? `${overtime.group_info?.time_in} - ${overtime.group_info?.time_out}`
              : `${overtime.time_in} - ${overtime.time_out}`
          }}
        </span>
      </div>
    </div>

    <!-- Creator Info -->
    <div class="mb-2 text-sm">
      <div class="flex items-center justify-between">
        <span class="text-gray-500">
          {{ $t('attendance.overtime.card.creator') }}:
        </span>
        <div class="text-right">
          <span class="font-medium text-gray-900">
            {{
              (isGroup
                ? overtime.group_info?.creator_info?.full_name
                : overtime.creator_info?.full_name) || $t('attendance.common.unknown')
            }}
          </span>
          <span v-if="!isGroup && overtime.creator_info?.staff_identifi" class="ml-1 text-gray-400">
            ({{ overtime.creator_info.staff_identifi }})
          </span>
          <span v-else class="ml-1 text-gray-400">
            ({{ overtime.group_info?.creator_info.staff_identifi }})
          </span>
        </div>
      </div>
    </div>

    <!-- Approver Info -->
    <div class="mb-3 text-sm">
      <div class="flex items-center justify-between">
        <span class="text-gray-500">
          {{ $t('attendance.overtime.card.approver') }}:
        </span>
        <span class="font-medium text-gray-900">
          {{
            isGroup
              ? overtime.group_info?.approver_info?.full_name
              : overtime.approver_info?.full_name || $t('attendance.common.unknown')
          }}
        </span>
      </div>
    </div>

    <!-- Reason -->
    <div class="mb-3 rounded-md bg-gray-50 p-2">
      <span class="text-xs font-medium text-gray-700">
        {{ $t('attendance.overtime.card.reason') }}:
      </span>
      <p class="mt-1 text-sm text-gray-600">{{ isGroup ? overtime.group_info?.reason : overtime.reason }}</p>
    </div>

    <div v-if="isGroup && currentEmployeeStatus == 'pending'">
      <div class="grid grid-cols-2 gap-3 text-sm">
        <Button variant="outline" class="flex items-center gap-2"
          :disabled="updateStatusOvertimeMutation.isPending.value" @click="handleApprove">
          <Check :size="16" />
          {{ $t('management.requests.actions.approve') }}
        </Button>
        <Button variant="outline-destructive" class="flex items-center gap-2"
          :disabled="updateStatusOvertimeMutation.isPending.value" @click="handleReject">
          <X :size="16" />
          {{ $t('management.requests.actions.reject') }}
        </Button>
      </div>
    </div>

    <!-- Rejection Reason -->
    <div v-if="overtime.status === Status.REJECTED" class="mb-3 rounded-md bg-red-50 p-2">
      <span class="text-xs font-medium text-red-700">
        {{ $t('attendance.overtime.card.rejection_reason') }}:
      </span>
      <p class="mt-1 text-sm text-red-600">{{ isGroup ? overtime.group_info?.reject_reason : overtime.reject_reason }}
      </p>
    </div>

    <div v-if="overtime.can_edit || overtime.group_info?.can_edit" class="border-t pt-3">
      <Button variant="outline" size="sm" class="w-full" @click="handleEdit">
        <PencilIcon class="mr-2 h-4 w-4" />
        {{ $t('attendance.register_overtime.edit_title') }}
      </Button>
    </div>
  </div>
</template>
