<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { useAttendanceForm } from '@/composables/useAttendanceForm';
import { useDateFormats } from '@/composables/useDateFormats';
import { FormMode } from '@/enums';
import { IonPage } from '@ionic/vue';
import {
  NAlert,
  NDatePicker,
  NForm,
  NFormItem,
  NInput,
  NSelect,
  NTag,
  NTimePicker,
  NCheckbox,
} from 'naive-ui';
import { Label } from '@/components/ui/label';
import { computed, onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute } from 'vue-router';
import { cn } from '@/lib/utils';
import { Skeleton } from '@/components/ui/skeleton';

const route = useRoute();
const { t } = useI18n();

const overtimeId = computed(() => route.params.id as string);

const {
  formValue,
  formRef,
  rules,
  alert,
  allStaffOptions,
  staffOptions,
  workShiftOptions,
  isLoading,
  groupMembers,
  isGroupOvertime,
  handleSubmit,
  handleCancel,
  populateForm,
  updateWorkShiftMutation,
} = useAttendanceForm(FormMode.UPDATE, overtimeId.value);

const { pickerConfigs } = useDateFormats();

const isSelectGroup = ref(false);

onMounted(() => {
  populateForm();
});
</script>

<template>
  <ion-page>
    <div class="scroll-container flex h-full flex-col items-center gap-y-4 p-4">
      <div v-if="isLoading" class="w-full space-y-6">
        <div>
          <Skeleton class="h-4 w-32 mb-2" />
          <Skeleton class="h-10 w-full" />
        </div>
        <div>
          <Skeleton class="h-4 w-48 mb-2" />
          <Skeleton class="h-10 w-full" />
        </div>
        <div>
          <Skeleton class="h-4 w-40 mb-2" />
          <Skeleton class="h-10 w-full" />
        </div>
        <div>
          <Skeleton class="h-4 w-28 mb-2" />
          <Skeleton class="h-10 w-full" />
        </div>
        <div>
          <Skeleton class="h-4 w-28 mb-2" />
          <Skeleton class="h-10 w-full" />
        </div>
        <div>
          <Skeleton class="h-4 w-32 mb-2" />
          <Skeleton class="h-20 w-full" />
        </div>
        <div class="flex gap-x-4">
          <Skeleton class="h-12 w-full" />
        </div>
      </div>
      <div v-else class="w-full">
        <n-alert v-if="alert.visible" :title="alert.title" :type="alert.type" closable @close="alert.visible = false"
          bordered class="mb-4 w-full">
          {{ alert.message }}
        </n-alert>


        <n-form ref="formRef" :model="formValue" :rules="rules" class="w-full">
          <n-form-item :label="t('attendance.register_overtime.additional_day')" path="additional_day" class="w-full"
            required>
            <n-date-picker v-model:formatted-value="formValue.additional_day" type="date"
              :format="pickerConfigs.attendance.date.format" :value-format="pickerConfigs.attendance.date.valueFormat"
              :placeholder="t('attendance.register_overtime.additional_day_placeholder')
                " clearable class="w-full" />
          </n-form-item>

          <n-form-item :label="t('attendance.register_overtime.work_shift')" class="w-full" path="shift_id" required>
            <n-select v-model:value="formValue.shift_id" filterable :options="workShiftOptions" :placeholder="t('attendance.register_overtime.work_shift_placeholder')
              " />
          </n-form-item>
          <div v-if="!isSelectGroup && groupMembers.length > 0" class="mb-4">
            <Label class="mb-3 block font-medium">{{ t('leaves.new_request.staff') }} ({{ groupMembers.length
            }})</Label>

            <!-- Table Layout -->
            <div class="border border-gray-200 rounded-sm overflow-hidden">
              <div class="bg-gray-50 px-4 py-2 border-b border-gray-200">
                <div class="grid grid-cols-3 gap-4 text-xs font-medium text-gray-700">
                  <div>{{ t('attendance.update.name') }}</div>
                  <div>{{ t('attendance.update.staff_id') }}</div>
                  <div>{{ t('attendance.update.status') }}</div>
                </div>
              </div>
              <div class="divide-y divide-gray-200">
                <div v-for="member in groupMembers" :key="member.id" class="px-4 py-3 hover:bg-gray-50">
                  <div class="grid grid-cols-3 gap-4 items-center">
                    <div class="text-sm font-medium text-gray-900">{{ member.full_name }}</div>
                    <div class="text-sm text-gray-500">{{ member.staff_identifi || 'N/A' }}</div>
                    <div>
                      <n-tag v-if="member.employee_status_text"
                        :type="member.employee_status === 1 ? 'success' : member.employee_status === 2 ? 'error' : 'warning'"
                        size="small">
                        {{ member.employee_status_text }}
                      </n-tag>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div v-if="isGroupOvertime" class="">
            <n-checkbox v-model:checked="isSelectGroup" class="text-sm" :class="cn(!isSelectGroup && 'mb-4')">
              {{ t('leaves.new_request.staff_placeholder') }}
            </n-checkbox>
          </div>
          <n-form-item v-if="isSelectGroup" path="employees">
            <n-select v-model:value="formValue.employees" :options="staffOptions"
              :placeholder="t('leaves.new_request.staff_placeholder')" multiple filterable />
          </n-form-item>
          <div class="grid grid-cols-2 gap-4">
            <n-form-item :label="t('attendance.register_overtime.time_in')" path="time_in" class="w-full">
              <n-time-picker v-model:value="formValue.time_in" :format="pickerConfigs.attendance.time.format"
                :placeholder="t('attendance.register_overtime.time_in_placeholder')
                  " clearable class="w-full" disabled />
            </n-form-item>

            <n-form-item :label="t('attendance.register_overtime.time_out')" path="time_out" class="w-full">
              <n-time-picker v-model:value="formValue.time_out" :format="pickerConfigs.attendance.time.format"
                :placeholder="t('attendance.register_overtime.time_out_placeholder')
                  " clearable class="w-full" disabled />
            </n-form-item>
          </div>
          <n-form-item :label="t('attendance.register_overtime.timekeeping_value')" path="timekeeping_value"
            class="w-full">
            <n-input v-model:value="formValue.timekeeping_value" :placeholder="t('attendance.register_overtime.timekeeping_value_placeholder')
              " class="w-full" disabled />
          </n-form-item>

          <n-form-item :label="t('attendance.register_overtime.approver')" path="approver_id" required>
            <n-select v-model:value="formValue.approver_id" filterable :options="allStaffOptions" :placeholder="t('attendance.register_overtime.approver_placeholder')
              " />
          </n-form-item>

          <n-form-item :label="t('attendance.register_overtime.reason')" path="reason" class="w-full" required>
            <n-input v-model:value="formValue.reason" type="textarea" :placeholder="t('attendance.register_overtime.reason_placeholder')
              " :autosize="{ minRows: 3, maxRows: 7 }" class="!h-[125px] w-full" />
          </n-form-item>

          <div class="flex items-center gap-x-4">
            <Button type="button" variant="outline" size="lg" class="flex-1" @click="handleCancel">
              {{ t('common.cancel') }}
            </Button>
            <Button type="submit" size="lg" class="flex-1" @click="handleSubmit"
              :disabled="updateWorkShiftMutation.isPending.value" :loading="updateWorkShiftMutation.isPending.value">
              {{ t('attendance.register_overtime.update') }}
            </Button>
          </div>
        </n-form>
      </div>
    </div>
  </ion-page>
</template>
