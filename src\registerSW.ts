import i18n from '@/plugins/i18n';
import { registerSW } from 'virtual:pwa-register';
import { toast } from 'vue-sonner';

const updateSW = registerSW({
  onNeedRefresh() {
    const message = i18n.global.t('pwa.new_version');
    const confirmUpdate = i18n.global.t('pwa.confirm_update');

    if (confirm(`${message}\n${confirmUpdate}`)) {
      updateSW();
    }
  },
  onOfflineReady() {
    const offlineReadyMessage = i18n.global.t('pwa.offline_ready');
    toast.info(offlineReadyMessage);
  },
});
