<script setup lang="ts">
import { Skeleton } from '@/components/ui/skeleton';
import { useLocale } from '@/composables/useLocale';
import * as ROUTES from '@/constants/routes';
import { RoleName } from '@/enums/role';
import { isManager } from '@/helpers/staff-helper';
import { useAuthRoleStore } from '@/stores/auth-role';
import { getLocalizedPath } from '@/utils/localized-navigation';
import { IonIcon } from '@ionic/vue';
import {
  businessOutline,
  cashOutline,
  documentTextOutline,
  folderOpenOutline,
  personOutline,
  timerOutline,
  walletOutline,
} from 'ionicons/icons';
import { NCard, NList, NListItem, NThing } from 'naive-ui';
import { computed, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';

const { t } = useI18n();
const { locale } = useLocale();
const router = useRouter();

defineProps({
  isLoading: Boolean,
});

const authRoleStore = useAuthRoleStore();
const userRole = computed(() => authRoleStore.roleName || '');

const quickLinkTitle = ref(t('home.quick_links'));

const allQuickLinks = ref([
  {
    title: t('home.register_overtime'),
    href: ROUTES.REGISTER_OVERTIME,
    iconName: 'timerOutline',
  },
  {
    title: t('home.request_leave'),
    href: ROUTES.NEW_LEAVE_APPLICATIONS,
    iconName: 'documentTextOutline',
  },
  // {
  //   title: t('home.claim_expense'),
  //   href: ROUTES.NEW_EXPENSE_CLAIMS,
  //   iconName: 'cashOutline',
  // },
  {
    title: t('home.manage_registrations'),
    href: ROUTES.MANAGEMENT_REQUESTS,
    iconName: 'folderOpenOutline',
    requiredRole: RoleName.MANAGER,
  },
]);

const quickLinks = computed(() => {
  return allQuickLinks.value.filter((link) => {
    if (!link.requiredRole) {
      return true;
    }

    if (link.requiredRole === RoleName.MANAGER) {
      return isManager(userRole.value);
    }

    return true;
  });
});

const getIonicIcon = (iconName: string) => {
  const iconMap: Record<string, typeof personOutline> = {
    personOutline,
    documentTextOutline,
    cashOutline,
    walletOutline,
    businessOutline,
    timerOutline,
    folderOpenOutline,
  };
  return iconMap[iconName] || personOutline;
};

const handleClick = (href: string) => {
  const newHref = getLocalizedPath(href, locale.value);
  router.push(newHref);
};
</script>

<template>
  <div class="my-4 w-full">
    <div v-if="isLoading">
      <Skeleton class="mb-3 h-8 w-48" />
    </div>
    <h5 v-else class="mb-3 text-xl font-semibold text-slate-900 capitalize">
      {{ quickLinkTitle }}
    </h5>
    <n-card v-if="!isLoading" class="!rounded-md shadow" content-class="!p-0">
      <n-list>
        <n-list-item
          v-for="(item, index) in quickLinks"
          :key="index"
          @click="handleClick(item.href)"
          class="cursor-pointer hover:bg-gray-50"
        >
          <n-thing>
            <div class="flex items-center space-x-3">
              <ion-icon :icon="getIonicIcon(item.iconName)" class="size-5" />
              <span class="text-base">{{ item.title }}</span>
            </div>
          </n-thing>
        </n-list-item>
      </n-list>
    </n-card>
    <div v-else>
      <Skeleton class="mb-2 h-12 w-full" />
      <Skeleton class="mb-2 h-12 w-full" />
      <Skeleton class="mb-2 h-12 w-full" />
    </div>
  </div>
</template>

<style scoped>
:deep(.n-list-item) {
  padding: 12px 16px;
}

:deep(.n-thing) {
  line-height: 1;
}

:deep(.n-thing-avatar) {
  margin-right: 12px;
}

:deep(.n-icon) {
  color: var(--n-text-color);
}
</style>
