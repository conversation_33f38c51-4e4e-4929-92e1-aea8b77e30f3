import { SUPPORTED_LOCALES, type SupportedLocale } from '@/constants/locales';
import i18n from '@/plugins/i18n';
import router from '@/router';
import { getLocalizedPath } from '@/utils/localized-navigation';
import { useLocalStorage } from '@vueuse/core';

const localeMap: Record<string, SupportedLocale> = {
  english: 'en',
  vietnamese: 'vi',
  japanese: 'ja',
};

export function setLocaleFromLanguage(language: string) {
  const selectedLocale: SupportedLocale = localeMap[language] || 'en';

  i18n.global.locale.value = selectedLocale;

  const locale = useLocalStorage('locale', 'en');
  locale.value = selectedLocale;

  updateUrlWithLanguage(selectedLocale);
}

function updateUrlWithLanguage(locale: string) {
  if (typeof window !== 'undefined' && router) {
    const currentRoute = router.currentRoute.value;

    let path = currentRoute.path;

    const hasLocalePrefix = SUPPORTED_LOCALES.some(
      (supportedLocale) =>
        path.startsWith(`/${supportedLocale}/`) ||
        path === `/${supportedLocale}`,
    );

    if (hasLocalePrefix) {
      const pathParts = path.split('/').filter(Boolean);
      pathParts.shift();
      path = pathParts.length > 0 ? '/' + pathParts.join('/') : '/';
    }

    const newPath = getLocalizedPath(path, locale as SupportedLocale);

    router
      .replace({
        path: newPath,
        query: currentRoute.query,
        hash: currentRoute.hash,
      })
      .catch((err: Error) => {
        console.warn('Navigation error:', err);
      });
  }
}
