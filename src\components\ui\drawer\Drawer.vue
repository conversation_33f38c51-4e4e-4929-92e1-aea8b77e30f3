<script lang="ts" setup>
import { useForwardPropsEmits } from 'reka-ui';
import type { DrawerRootEmits, DrawerRootProps } from 'vaul-vue';
import { DrawerRoot } from 'vaul-vue';
import { ref, watch } from 'vue';

const props = withDefaults(defineProps<DrawerRootProps>(), {
  shouldScaleBackground: true,
});

const emits = defineEmits<DrawerRootEmits>();

const forwarded = useForwardPropsEmits(props, emits) as unknown as Record<
  string,
  unknown
>;

const isOpen = ref(false);

watch(isOpen, (newVal) => {
  if (newVal) {
    setTimeout(() => {
      const drawer = document.querySelector(
        '[data-slot="drawer-content"]',
      ) as HTMLElement;
      drawer?.focus();
    }, 0);
  }
});
</script>

<template>
  <DrawerRoot data-slot="drawer" v-bind="forwarded">
    <slot />
  </DrawerRoot>
</template>
