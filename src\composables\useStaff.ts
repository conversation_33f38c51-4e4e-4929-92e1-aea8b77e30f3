import { getStaffApprovers, getStaffs } from '@/services/staff.service';

import { useQuery } from '@tanstack/vue-query';

export const useStaff = () => {
  const staffQuery = useQuery({
    queryKey: ['staffs'],
    queryFn: getStaffs,
    select: (res) => res.data,
  });

  const staffApprovedQuery = useQuery({
    queryKey: ['staff-approvers'],
    queryFn: getStaffApprovers,
    select: (res) => res.data,
  });


  return {
    staffQuery,
    staffApprovedQuery,
  };
};
