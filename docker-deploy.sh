#!/bin/bash

# Docker deployment script for Sharp Vue.js application
# Usage: ./docker-deploy.sh [options]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
COMPOSE_FILE="docker-compose.yml"
SERVICE_NAME="sharp-app"
ACTION="up"
DETACHED=true

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS] [ACTION]"
    echo ""
    echo "Actions:"
    echo "  up          Start services (default)"
    echo "  down        Stop and remove services"
    echo "  restart     Restart services"
    echo "  logs        Show service logs"
    echo "  status      Show service status"
    echo "  build       Build and start services"
    echo ""
    echo "Options:"
    echo "  -f, --file FILE       Docker compose file (default: docker-compose.yml)"
    echo "  -s, --service NAME    Service name (default: sharp-app)"
    echo "  --no-detach          Run in foreground (don't use -d flag)"
    echo "  -h, --help           Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                    # Start services in background"
    echo "  $0 down              # Stop services"
    echo "  $0 logs              # Show logs"
    echo "  $0 --no-detach up    # Start in foreground"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -f|--file)
            COMPOSE_FILE="$2"
            shift 2
            ;;
        -s|--service)
            SERVICE_NAME="$2"
            shift 2
            ;;
        --no-detach)
            DETACHED=false
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        up|down|restart|logs|status|build)
            ACTION="$1"
            shift
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Check if docker-compose file exists
if [[ ! -f "$COMPOSE_FILE" ]]; then
    print_error "Docker compose file not found: $COMPOSE_FILE"
    exit 1
fi

# Check if Docker and docker-compose are available
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed or not in PATH."
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    print_error "docker-compose is not installed or not in PATH."
    exit 1
fi

# Execute the requested action
case $ACTION in
    up)
        print_status "Starting services..."
        if [[ "$DETACHED" == true ]]; then
            docker-compose -f "$COMPOSE_FILE" up -d
        else
            docker-compose -f "$COMPOSE_FILE" up
        fi
        print_success "Services started successfully!"
        print_status "Application should be available at: http://localhost:8085"
        ;;
    
    down)
        print_status "Stopping services..."
        docker-compose -f "$COMPOSE_FILE" down
        print_success "Services stopped successfully!"
        ;;
    
    restart)
        print_status "Restarting services..."
        docker-compose -f "$COMPOSE_FILE" restart "$SERVICE_NAME"
        print_success "Services restarted successfully!"
        ;;
    
    logs)
        print_status "Showing logs for $SERVICE_NAME..."
        docker-compose -f "$COMPOSE_FILE" logs -f "$SERVICE_NAME"
        ;;
    
    status)
        print_status "Service status:"
        docker-compose -f "$COMPOSE_FILE" ps
        ;;
    
    build)
        print_status "Building and starting services..."
        if [[ "$DETACHED" == true ]]; then
            docker-compose -f "$COMPOSE_FILE" up --build -d
        else
            docker-compose -f "$COMPOSE_FILE" up --build
        fi
        print_success "Services built and started successfully!"
        ;;
    
    *)
        print_error "Unknown action: $ACTION"
        show_usage
        exit 1
        ;;
esac
