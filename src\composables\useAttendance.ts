import { PAGE_SIZE } from '@/constants';
import type { ApproveOvertimeRequestPayload, ApproveOvertimeRequestResponse, OvertimeRequest, OvertimeRequestResponse, UpdateEmployeeStatusPayload } from '@/interfaces/attendance';
import {
  approveOvertimeRequest,
  getAttendanceLogs,
  getGroupOvertimeRequest,
  getLatestOvertimeRequest,
  getManagementOvertimeRequests,
  getOvertimeRequest,
  getOvertimeRequests,
  getWorkShifts,
  registerOvertime,
  updateWorkShift,
  updateWorkShiftGroup,
  updateStatusOvertime,
  getOvertimeStats,
} from '@/services/attendance.service';

import { useMutation, useQuery, useQueryClient } from '@tanstack/vue-query';
import { type Ref } from 'vue';

export const useAttendance = () => {
  const queryClient = useQueryClient();

  const registerOvertimeMutation = useMutation<
    OvertimeRequestResponse,
    Error,
    OvertimeRequest
  >({
    mutationFn: registerOvertime,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['overtime-requests'] });
    },
  });

  const updateWorkShiftMutation = useMutation<
    OvertimeRequestResponse,
    Error,
    { id: number; data: OvertimeRequest }
  >({
    mutationFn: ({ id, data }) => updateWorkShift(id, data),
    onSuccess: (response, variables) => {
      queryClient.invalidateQueries({ queryKey: ['overtime-requests'] });
      queryClient.invalidateQueries({
        queryKey: ['overtime-request', variables.id],
      });
    },
  });
      const updateWorkShiftGroupMutation = useMutation<
      OvertimeRequestResponse,
      Error,
      { group_code: string; data: OvertimeRequest }
    >({
      mutationFn: ({ group_code, data }) => updateWorkShiftGroup(group_code, data),
      onSuccess: (response, variables) => {
        queryClient.invalidateQueries({ queryKey: ['overtime-group-requests'] });
        queryClient.invalidateQueries({
          queryKey: ['overtime-group-request', variables.group_code],
        });
      },
    });

  const approveOvertimeRequestMutation = useMutation<
    ApproveOvertimeRequestResponse,
    Error,
    ApproveOvertimeRequestPayload
  >({
    mutationFn: approveOvertimeRequest,
    onSuccess: () => {
      // Invalidate all overtime related queries to refresh data
      queryClient.invalidateQueries({ queryKey: ['overtime-requests'] });
      queryClient.invalidateQueries({
        queryKey: ['management-overtime-requests'],
      });
    },
  });

 const updateStatusOvertimeMutation = useMutation<
  OvertimeRequestResponse,
  Error,
  UpdateEmployeeStatusPayload
>({
  mutationFn: (payload) => updateStatusOvertime(payload),
  onSuccess: () => {
    queryClient.invalidateQueries({ queryKey: ['overtime-requests'] });
    queryClient.invalidateQueries({ queryKey: ['latest-overtime-requests'] });
  },
});

  const workShiftQuery = useQuery({
    queryKey: ['work-shifts'],
    queryFn: getWorkShifts,
  });

  const getOvertimeStatstics = useQuery({
    queryKey: ['overtime-stats'],
    queryFn: () => getOvertimeStats(),
  });


  const createOvertimeQuery = (filters: Ref<Record<string, any>>) => {
    return useQuery({
      queryKey: ['overtime-requests', filters],
      queryFn: () => getOvertimeRequests(filters.value),
    });
  };

  const managementOvertimeQuery = (filters: Ref<Record<string, any>>) => {
    return useQuery({
      queryKey: ['management-overtime-requests', filters],
      queryFn: () => getManagementOvertimeRequests(filters.value),
    });
  };

  const attendanceLogsQuery = (filters: Ref<Record<string, any>>) => {
    return useQuery({
      queryKey: ['attendance-logs', filters],
      queryFn: () => getAttendanceLogs(filters.value),
    });
  };

  const overtimeItemQuery = (id: number, enabled: boolean = true) => {
    return useQuery({
      queryKey: ['overtime-request', id],
      queryFn: () => getOvertimeRequest(id),
      select: (data) => data.data,
      enabled: enabled && !!id,
    });
  };

  const groupOvertimeItemQuery = (group_code: string, enabled: boolean = true) => {
    return useQuery({
      queryKey: ['overtime-group-request', group_code],
      queryFn: () => getGroupOvertimeRequest(group_code),
      select: (data) => data.data,
      enabled: enabled && !!group_code,
    });
  };
  const latestOvertimeQuery = (limit: number = PAGE_SIZE) => {
    return useQuery({
      queryKey: ['latest-overtime-requests', limit],
      queryFn: () => getLatestOvertimeRequest(limit),
    });
  };

  return {
    // Mutations
    registerOvertimeMutation,
    updateWorkShiftMutation,
    updateWorkShiftGroupMutation,
    approveOvertimeRequestMutation,
    updateStatusOvertimeMutation,

    // Queries
    workShiftQuery,
    getOvertimeStatstics,
    createOvertimeQuery,
    managementOvertimeQuery,
    attendanceLogsQuery,
    overtimeItemQuery,
    groupOvertimeItemQuery,
    latestOvertimeQuery,
  };
};
