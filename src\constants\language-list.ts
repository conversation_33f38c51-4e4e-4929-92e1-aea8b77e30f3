import { LanguageCode, LanguageValue } from '@/enums/language';

export interface Language {
  name: string;
  code: LanguageCode;
  language: LanguageValue;
  flagSrc?: string;
}

export const languages: Language[] = [
  {
    name: 'Vietnamese',
    code: LanguageCode.Vietnamese,
    language: LanguageValue.Vietnamese,
    flagSrc: '/images/flags/vi.png',
  },
  {
    name: 'English',
    code: LanguageCode.English,
    language: LanguageValue.English,
    flagSrc: '/images/flags/en.png',
  },
  {
    name: 'Japanese',
    code: LanguageCode.Japanese,
    language: LanguageValue.Japanese,
    flagSrc: '/images/flags/ja.png',
  },
];
