<script setup lang="ts">
import { Skeleton } from '@/components/ui/skeleton';
import { ATTENDANCE_HISTORY } from '@/constants/routes';
import dayjs from 'dayjs';
import { NCard } from 'naive-ui';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

defineProps({
  fullName: String,
  isLoading: Boolean,
});

const lastTime = dayjs().format('HH:mm - DD/MM/YYYY');
</script>

<template>
  <n-card class="w-full shadow">
    <div v-if="!isLoading" class="flex flex-col gap-y-2">
      <h2 class="text-lg font-bold text-slate-900">
        {{ t('home.greeting', { name: fullName }) }}
      </h2>
      <div
        class="mt-1.5 mb-2.5 flex flex-wrap items-center gap-1 text-xs font-medium text-gray-500"
      >
        <span>{{ t('home.last_login', { time: lastTime }) }}</span>
      </div>
      <div class="flex items-center gap-2">
        <RouterLink
          :to="ATTENDANCE_HISTORY"
          class="text-gray-500 underline underline-offset-2 hover:text-gray-700"
        >
          {{ t('home.view_attendance_history') }}
        </RouterLink>
      </div>
    </div>
    <div v-else>
      <Skeleton class="mb-4 h-6 w-48" />
      <Skeleton class="h-4 w-32" />
    </div>
  </n-card>
</template>

<style scoped>
.small-dot {
  transform: translateY(-1px);
}
</style>
