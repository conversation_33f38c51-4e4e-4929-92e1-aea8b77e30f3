# Version control
.git
.gitignore
.gitlab-ci.yml

# Node.js
node_modules
npm-debug.log
yarn-debug.log
yarn-error.log

# Build files
dist
dist-ssr
dev-dist
build
coverage
*.local

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Docker
Dockerfile
docker-compose.yml
.dockerignore

# Deployment scripts
deploy.sh

# Testing and development
test-hooks.sh
.husky/_
cypress/videos/
cypress/screenshots/

# Cache files
.npm
.eslintcache
*.tsbuildinfo
.stylelintcache

# Logs
logs
*.log

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
