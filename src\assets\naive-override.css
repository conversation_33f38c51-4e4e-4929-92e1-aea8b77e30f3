@import 'tailwindcss';

@layer utilities {
  .n-date-panel {
    --n-item-size: 32px !important;
    --n-item-cell-width: 38px !important;
    --n-item-cell-height: 35px !important;
    --n-arrow-size: 18px !important;
    --n-item-color-active: var(--c-secondary) !important;
  }

  .n-select-menu {
    --n-option-text-color-active: var(--c-secondary) !important;
    --n-option-check-color: var(--c-secondary) !important;
  }

  .n-base-selection {
    --n-border-active: 1px solid var(--c-secondary) !important;
    --n-border-focus: 1px solid var(--c-secondary) !important;
    --n-border-hover: 1px solid var(--c-secondary) !important;
    --n-box-shadow-active: 0 0 0 2px rgba(22, 119, 255, 0.5) !important;
    --n-box-shadow-focus: 0 0 0 2px rgba(22, 119, 255, 0.502) !important;
    --n-caret-color: var(--c-secondary) !important;
    --n-loading-color: var(--c-secondary) !important;
    --n-border-radius: 6px !important;
    --n-height: 36px !important;
  }

  .n-form-item .n-form-item-label {
    font-size: 16px !important;
    font-weight: 500 !important;
  }

  .n-input {
    --n-caret-color: var(--c-secondary) !important;
    --n-border-hover: 1px solid var(--c-secondary) !important;
    --n-border-focus: 1px solid var(--c-secondary) !important;
    --n-loading-color: var(--c-secondary) !important;
    --n-box-shadow-focus: 0 0 0 2px rgba(22, 119, 255, 0.5) !important;
    --n-border-radius: 6px !important;
    --n-height: 36px !important;
  }

  .n-checkbox {
    --n-border-checked: 1px solid var(--c-secondary) !important;
    --n-border-focus: 1px solid var(--c-secondary) !important;
    --n-border-hover: 1px solid var(--c-secondary) !important;
    --n-box-shadow-focus: 0 0 0 2px rgba(22, 119, 255, 0.5) !important;
    --n-color-checked: var(--c-secondary) !important;
    --n-box-shadow-hover: none !important;
  }

  .n-button {
    --n-border-radius: 8px !important;
    --n-font-weight: 500 !important;
    --n-border-hover: 1px solid var(--c-secondary) !important;
    --n-text-color-hover: var(--c-secondary) !important;
  }

  .n-button.n-button--primary-type {
    --n-color: var(--c-secondary) !important;
    --n-color-hover: var(--c-secondary) !important;
    --n-text-color-hover: white !important;
    --n-border: 1px solid var(--c-secondary) !important;
  }

  .n-list {
    --n-border-radius: 10px !important;
    overflow: hidden !important;
  }

  .n-input-number {
    width: 100%;
  }

  .n-input-wrapper {
    resize: none !important;
  }

  .n-form-item .n-form-item-label {
    font-size: 14px !important;
    cursor: pointer;
  }

  .n-tabs {
    --n-bar-color: var(--c-secondary) !important;
    --n-tab-text-color-active: var(--c-secondary) !important;
    --n-tab-text-color-hover: var(--c-secondary) !important;
  }

  .n-empty .n-empty__icon + .n-empty__description {
    margin-top: 24px !important;
  }
}
