export enum Status {
  PENDING = 0,
  APPROVED = 1,
  REJECTED = 2,
}

export enum ActionType {
  APPROVE = 'approve',
  REJECT = 'reject',
}

export enum RequestType {
  REQUISITION = 'requisition',
  ADDITIONAL = 'additional',
}

export enum RegistrationType {
  SELF = 'self',
  GROUP = 'group',
}

export enum FormMode {
  CREATE = 'create',
  UPDATE = 'update',
}

export enum AttendanceFormType {
  OVERTIME = 'overtime',
  WORKSHIFT = 'workshift',
}

export * from './management';
