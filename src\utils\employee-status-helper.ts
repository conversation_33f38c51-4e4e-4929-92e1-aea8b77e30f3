import type { Overtime, OvertimeEmployee } from '@/interfaces/attendance';

export const getCurrentEmployeeStatus = (
  overtime: Overtime,
  currentUserId: number
): string | null => {
  if (!currentUserId) return null;

  const isGroup = !!overtime.group_info;

  if (isGroup && overtime.employees && Array.isArray(overtime.employees)) {
    const currentEmployee = overtime.employees.find(
      (emp: OvertimeEmployee) => emp.employee_info.staff_id === currentUserId
    );
    return currentEmployee ? currentEmployee.employee_status_text : null;
  }

  if (isGroup && overtime.group_info?.employees && Array.isArray(overtime.group_info.employees)) {
    const currentEmployee = overtime.group_info.employees.find(
      (emp: OvertimeEmployee) => emp.employee_info.staff_id === currentUserId
    );
    return currentEmployee ? currentEmployee.employee_status_text : null;
  }

  return null;
};

export const isCurrentUserInEmployeeList = (
  overtime: Overtime,
  currentUserId: number
): boolean => {
  return getCurrentEmployeeStatus(overtime, currentUserId) !== null;
};
