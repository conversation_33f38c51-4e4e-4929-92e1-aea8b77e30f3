import type {
  Leave,
  LeaveMutationResponse,
  LeaveRequest,
  LeaveResponse,
} from '@/interfaces/leave';

import apiClient from '@/api/axios-instance';

export const getLeaveRequests = async (): Promise<LeaveResponse> => {
  const { data } = await apiClient.get<LeaveResponse>(
    '/timesheet/time-adjustments',
  );

  return data;
};

export const getLeaveRequest = async (id: string): Promise<Leave> => {
  const { data } = await apiClient.get(`/timesheet/time-adjustments/id/${id}`);

  return data;
};

export const createLeaveRequest = async (
  payload: LeaveRequest,
): Promise<LeaveMutationResponse> => {
  const { data } = await apiClient.post('/timesheet/time-adjustments', payload);

  return data;
};

export const updateLeaveRequest = async (
  id: number,
  payload: Partial<LeaveRequest>,
): Promise<LeaveMutationResponse> => {
  const { data } = await apiClient.put(
    `/timesheet/time-adjustments/id/${id}`,
    payload,
  );

  return data;
};
