import {
  DEFAULT_LOCALE,
  SUPPORTED_LOCALES,
  type SupportedLocale,
} from '@/constants/locales';
import i18n, { setLocale } from '@/plugins/i18n';
import { useStorage } from '@vueuse/core';
import type { NavigationGuardNext, RouteLocationNormalized } from 'vue-router';
import { toast } from 'vue-sonner';

export async function localeMiddleware(
  to: RouteLocationNormalized,
  _from: RouteLocationNormalized,
  next: NavigationGuardNext,
) {
  const pathParts = to.path.split('/').filter(Boolean);
  const firstPart = pathParts[0];

  const storedLocale = useStorage<SupportedLocale>('locale', DEFAULT_LOCALE);

  if (firstPart && SUPPORTED_LOCALES.includes(firstPart as SupportedLocale)) {
    try {
      await setLocale(firstPart as SupportedLocale);
      storedLocale.value = firstPart as SupportedLocale;
      return next();
    } catch (error) {
      toast.error(i18n.global.t('common.toast.error.set_locale'), {
        description:
          error instanceof Error
            ? error.message
            : i18n.global.t('common.toast.error.unknown'),
      });
    }
  }

  const targetLocale = storedLocale.value;
  if (!firstPart || !SUPPORTED_LOCALES.includes(firstPart as SupportedLocale)) {
    return next(`/${targetLocale}${to.fullPath}`);
  }

  next();
}
